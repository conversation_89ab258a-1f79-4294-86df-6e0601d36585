<?php
// cli_commands.php - Execute Supabase CLI commands to list projects and databases
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase CLI Commands</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0d1117;
            color: #c9d1d9;
            padding: 20px;
            margin: 0;
        }
        .terminal {
            background: #161b22;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #30363d;
            max-width: 1200px;
            margin: 0 auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header {
            color: #58a6ff;
            border-bottom: 1px solid #30363d;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .command {
            background: #21262d;
            padding: 15px;
            border-left: 3px solid #238636;
            margin: 15px 0;
            border-radius: 6px;
        }
        .output {
            background: #0d1117;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #30363d;
        }
        .success { color: #56d364; }
        .error { color: #f85149; }
        .warning { color: #d29922; }
        .info { color: #58a6ff; }
        .prompt { color: #7c3aed; }
        pre { margin: 0; white-space: pre-wrap; }
        .section {
            margin: 30px 0;
            padding: 20px 0;
            border-top: 1px solid #30363d;
        }
        .btn {
            background: #238636;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
        }
        .btn:hover {
            background: #2ea043;
        }
        .btn-secondary {
            background: #6e7681;
        }
        .btn-secondary:hover {
            background: #7d8590;
        }
    </style>
</head>
<body>
    <div class="terminal">
        <div class="header">
            <h1>🚀 SUPABASE CLI COMMAND CENTER</h1>
            <p>Execute Supabase CLI commands to list and manage your projects</p>
        </div>

        <?php
        if (isset($_POST['action'])) {
            $action = $_POST['action'];
            
            echo "<div class='section'>\n";
            echo "<span class='info'>🔄 EXECUTING COMMAND...</span>\n\n";
            
            switch ($action) {
                case 'list_projects':
                    echo "<div class='command'>$ supabase projects list</div>\n";
                    $output = shell_exec('supabase projects list 2>&1');
                    break;
                    
                case 'status':
                    echo "<div class='command'>$ supabase status</div>\n";
                    $output = shell_exec('supabase status 2>&1');
                    break;
                    
                case 'version':
                    echo "<div class='command'>$ supabase --version</div>\n";
                    $output = shell_exec('supabase --version 2>&1');
                    break;
                    
                case 'login_status':
                    echo "<div class='command'>$ supabase projects list --limit 1</div>\n";
                    $output = shell_exec('supabase projects list --limit 1 2>&1');
                    break;
                    
                case 'orgs':
                    echo "<div class='command'>$ supabase orgs list</div>\n";
                    $output = shell_exec('supabase orgs list 2>&1');
                    break;
                    
                default:
                    $output = "Unknown command";
            }
            
            echo "<div class='output'>\n";
            if ($output) {
                echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
                
                // Analyze output
                if (strpos($output, 'Error') !== false || strpos($output, 'not logged in') !== false) {
                    echo "<span class='error'>❌ Command failed or not logged in</span>\n";
                } elseif (strpos($output, 'supabase') !== false || !empty(trim($output))) {
                    echo "<span class='success'>✅ Command executed successfully</span>\n";
                }
            } else {
                echo "<span class='warning'>⚠️ No output received</span>\n";
            }
            echo "</div>\n";
            echo "</div>\n";
        }
        ?>

        <div class="section">
            <span class="info">🎯 QUICK ACTIONS</span>
            
            <form method="post" style="margin: 20px 0;">
                <button type="submit" name="action" value="version" class="btn">Check CLI Version</button>
                <button type="submit" name="action" value="login_status" class="btn">Check Login Status</button>
                <button type="submit" name="action" value="list_projects" class="btn">List Projects</button>
                <button type="submit" name="action" value="orgs" class="btn">List Organizations</button>
                <button type="submit" name="action" value="status" class="btn btn-secondary">Local Status</button>
            </form>
        </div>

        <div class="section">
            <span class="info">📋 MANUAL COMMANDS</span>
            
            <p>If the buttons above don't work, try these commands in your terminal:</p>
            
            <div class="command">
                <span class="prompt"># Check if Supabase CLI is installed</span><br>
                supabase --version
            </div>
            
            <div class="command">
                <span class="prompt"># Login to Supabase (opens browser)</span><br>
                supabase login
            </div>
            
            <div class="command">
                <span class="prompt"># List all your projects</span><br>
                supabase projects list
            </div>
            
            <div class="command">
                <span class="prompt"># List organizations you belong to</span><br>
                supabase orgs list
            </div>
            
            <div class="command">
                <span class="prompt"># Get detailed project info</span><br>
                supabase projects list --format json
            </div>
            
            <div class="command">
                <span class="prompt"># Create a new project</span><br>
                supabase projects create "My New Project" --org-id [ORG-ID]
            </div>
        </div>

        <div class="section">
            <span class="info">🔧 TROUBLESHOOTING</span>
            
            <div class="command">
                <span class="warning">If CLI is not installed:</span><br>
                npm install -g supabase
            </div>
            
            <div class="command">
                <span class="warning">If not logged in:</span><br>
                supabase login
            </div>
            
            <div class="command">
                <span class="warning">If commands fail:</span><br>
                supabase --help
            </div>
        </div>

        <div class="section">
            <span class="info">🌐 ALTERNATIVE: WEB DASHBOARD</span>
            
            <p>You can also check your projects via the web dashboard:</p>
            <div class="command">
                <span class="info">🔗 <a href="https://supabase.com/dashboard" target="_blank" style="color: #58a6ff;">https://supabase.com/dashboard</a></span>
            </div>
            
            <p>From there you can:</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li>View all your projects</li>
                <li>See project details and settings</li>
                <li>Access database connection strings</li>
                <li>Manage API keys</li>
                <li>View usage and billing</li>
            </ul>
        </div>

        <?php
        // Check if CLI is available
        echo "<div class='section'>\n";
        echo "<span class='info'>🔍 SYSTEM CHECK</span>\n\n";
        
        $cli_check = shell_exec('supabase --version 2>&1');
        if ($cli_check && strpos($cli_check, 'supabase') !== false) {
            echo "<span class='success'>✅ Supabase CLI is installed: " . trim($cli_check) . "</span>\n";
        } else {
            echo "<span class='error'>❌ Supabase CLI not found</span>\n";
            echo "<p>Install with: <code>npm install -g supabase</code></p>\n";
        }
        
        $node_check = shell_exec('node --version 2>&1');
        if ($node_check && strpos($node_check, 'v') === 0) {
            echo "<span class='success'>✅ Node.js: " . trim($node_check) . "</span>\n";
        } else {
            echo "<span class='error'>❌ Node.js not found</span>\n";
        }
        
        echo "</div>\n";
        ?>

        <div class="section">
            <span class="info">📚 USEFUL RESOURCES</span>
            
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><a href="https://supabase.com/docs/guides/cli" target="_blank" style="color: #58a6ff;">Supabase CLI Documentation</a></li>
                <li><a href="https://supabase.com/dashboard" target="_blank" style="color: #58a6ff;">Supabase Dashboard</a></li>
                <li><a href="https://supabase.com/docs" target="_blank" style="color: #58a6ff;">Supabase Documentation</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
