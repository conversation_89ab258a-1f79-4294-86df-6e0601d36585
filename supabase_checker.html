<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Database Checker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], input[type="password"], input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #10b981;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .results-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .connection-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #10b981;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            background: #f9fafb;
            padding: 10px;
            border-radius: 6px;
        }

        .info-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .info-value {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            word-break: break-all;
        }

        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .instructions h3 {
            color: #92400e;
            margin-bottom: 10px;
        }

        .instructions ol {
            margin-left: 20px;
            color: #78350f;
        }

        .instructions li {
            margin-bottom: 5px;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Supabase Database Checker</h1>
            <p>Check your Supabase organization and list databases</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 How to Get Your Supabase Connection Details:</h3>
                <ol>
                    <li>Go to <a href="https://supabase.com/dashboard" target="_blank">Supabase Dashboard</a></li>
                    <li>Select your project</li>
                    <li>Go to Settings → Database</li>
                    <li>Copy the connection string from "Connection string" section</li>
                    <li>For API URL: Go to Settings → API → Project URL</li>
                    <li>For API Key: Go to Settings → API → Project API keys (use anon/public key)</li>
                </ol>
            </div>

            <div id="alert-container"></div>
            
            <div class="form-section">
                <h2>🔗 Supabase Connection Details</h2>
                <form id="connection-form">
                    <div class="form-group">
                        <label for="project-url">Project URL *</label>
                        <input type="url" id="project-url" placeholder="https://your-project.supabase.co" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="api-key">API Key (anon/public) *</label>
                        <input type="password" id="api-key" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db-connection">Database Connection String (Optional)</label>
                        <input type="password" id="db-connection" placeholder="postgresql://postgres:[password]@db.xxx.supabase.co:5432/postgres">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">🔍 Check Connection & List Tables</button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">🗑️ Clear</button>
                </form>
            </div>
            
            <div id="results-section" class="results-section" style="display: none;">
                <h2>📊 Connection Results</h2>
                <div id="results-content"></div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('connection-form');
        const alertContainer = document.getElementById('alert-container');
        const resultsSection = document.getElementById('results-section');
        const resultsContent = document.getElementById('results-content');

        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            await checkSupabaseConnection();
        });

        async function checkSupabaseConnection() {
            const projectUrl = document.getElementById('project-url').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const dbConnection = document.getElementById('db-connection').value.trim();

            if (!projectUrl || !apiKey) {
                showAlert('Please provide both Project URL and API Key', 'error');
                return;
            }

            showAlert('Checking connection...', 'info');
            resultsSection.style.display = 'none';

            try {
                // Test API connection by fetching project info
                const response = await fetch(`${projectUrl}/rest/v1/`, {
                    headers: {
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`API connection failed: ${response.status} ${response.statusText}`);
                }

                // Try to list tables
                const tablesResponse = await fetch(`${projectUrl}/rest/v1/?select=*`, {
                    headers: {
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/vnd.pgrst.object+json'
                    }
                });

                let tables = [];
                if (tablesResponse.ok) {
                    // Get table information from information_schema
                    const schemaResponse = await fetch(`${projectUrl}/rest/v1/rpc/get_schema_info`, {
                        method: 'POST',
                        headers: {
                            'apikey': apiKey,
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json'
                        }
                    });
                }

                showAlert('✅ Connection successful!', 'success');
                displayResults(projectUrl, apiKey, dbConnection, tables);

            } catch (error) {
                showAlert(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }

        function displayResults(projectUrl, apiKey, dbConnection, tables) {
            const projectId = extractProjectId(projectUrl);
            const region = extractRegion(projectUrl);

            resultsContent.innerHTML = `
                <div class="connection-info">
                    <h3>🎯 Project Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Project ID</div>
                            <div class="info-value">${projectId}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Region</div>
                            <div class="info-value">${region || 'Default'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">API URL</div>
                            <div class="info-value">${projectUrl}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">API Key (masked)</div>
                            <div class="info-value">${maskApiKey(apiKey)}</div>
                        </div>
                    </div>
                </div>

                <div class="connection-info">
                    <h3>🔗 Connection Strings</h3>
                    <p><strong>Direct Connection:</strong></p>
                    <div class="code-block">postgresql://postgres:[YOUR-PASSWORD]@db.${projectId}.supabase.co:5432/postgres</div>
                    
                    <p><strong>Pooled Connection (Session Mode):</strong></p>
                    <div class="code-block">postgres://postgres.${projectId}:[YOUR-PASSWORD]@aws-0-${region || 'us-east-1'}.pooler.supabase.com:5432/postgres</div>
                    
                    <p><strong>Pooled Connection (Transaction Mode):</strong></p>
                    <div class="code-block">postgres://postgres.${projectId}:[YOUR-PASSWORD]@aws-0-${region || 'us-east-1'}.pooler.supabase.com:6543/postgres</div>
                </div>

                <div class="connection-info">
                    <h3>📋 Next Steps</h3>
                    <ol>
                        <li>Use the connection strings above with your actual database password</li>
                        <li>For direct database access, use the Direct Connection string</li>
                        <li>For applications with many connections, use Pooled Connection</li>
                        <li>Check your database tables in the Supabase Dashboard</li>
                        <li>Set up Row Level Security (RLS) for your tables</li>
                    </ol>
                </div>
            `;

            resultsSection.style.display = 'block';
        }

        function extractProjectId(url) {
            const match = url.match(/https:\/\/([^.]+)\.supabase\.co/);
            return match ? match[1] : 'unknown';
        }

        function extractRegion(url) {
            // Try to extract region from URL if it contains region info
            const match = url.match(/https:\/\/[^.]+\.([^.]+)\.supabase\.co/);
            return match ? match[1] : null;
        }

        function maskApiKey(key) {
            if (key.length <= 8) return '*'.repeat(key.length);
            return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
        }

        function showAlert(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 
                             type === 'error' ? 'alert-error' : 'alert-info';
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        function clearForm() {
            form.reset();
            alertContainer.innerHTML = '';
            resultsSection.style.display = 'none';
        }
    </script>
</body>
</html>
