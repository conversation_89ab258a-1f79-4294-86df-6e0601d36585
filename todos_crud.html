<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todos CRUD - Supabase PWA</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Todo PWA">
    <meta name="description" content="A Progressive Web App for managing todos with offline capabilities">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Supabase JS -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- PWA Scripts -->
    <script src="/js/db.js"></script>
    <script src="/js/sync-manager.js"></script>
    <script src="/js/pwa-installer.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .hero-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .todo-card {
            transition: transform 0.3s ease;
            margin-bottom: 15px;
        }

        .todo-card:hover {
            transform: translateY(-2px);
        }

        .todo-card.completed {
            background: #e8f5e8;
        }

        .todo-task.completed {
            text-decoration: line-through;
            color: #666;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #4caf50;
        }

        .status-disconnected {
            background: #f44336;
        }

        .status-connecting {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .filter-chip {
            margin: 5px;
        }

        .edit-form {
            background: #f0f9ff;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-top: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="blue darken-2">
        <div class="nav-wrapper container">
            <a href="index.html" class="brand-logo">
                <i class="material-icons left">check_box</i>Todos CRUD
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="index.html"><i class="material-icons left">home</i>Dashboard</a></li>
                <li><a href="supabase_app.html"><i class="material-icons left">explore</i>Explorer</a></li>
                <li>
                    <a href="#" id="connection-status-nav">
                        <span class="status-indicator status-connecting" id="status-indicator"></span>
                        <span id="status-text">Connecting...</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><div class="user-view blue darken-2">
            <div class="background">
                <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); height: 100%;"></div>
            </div>
            <a href="#"><span class="white-text name">Todos CRUD</span></a>
            <a href="#"><span class="white-text email">Task Management</span></a>
        </div></li>
        <li><a href="index.html"><i class="material-icons">home</i>Dashboard</a></li>
        <li><a href="supabase_app.html"><i class="material-icons">explore</i>Database Explorer</a></li>
        <li><div class="divider"></div></li>
        <li><a href="#stats-section"><i class="material-icons">assessment</i>Statistics</a></li>
        <li><a href="#add-section"><i class="material-icons">add</i>Add Todo</a></li>
        <li><a href="#todos-section"><i class="material-icons">list</i>Todos List</a></li>
    </ul>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container center-align">
            <h2>📝 Todos CRUD Application</h2>
            <p>Complete Todo Management with Supabase</p>
            <div id="connection-status">
                <span class="status-indicator status-connecting" id="nav-status-indicator"></span>
                <span id="nav-status-text">Connecting...</span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alert Container -->
        <div id="alert-container"></div>

        <!-- Statistics Section -->
        <section id="stats-section" class="section">
            <h4 class="center-align">
                <i class="material-icons large">assessment</i> Statistics
            </h4>
            <div class="row">
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="total-todos">0</div>
                        <div>Total Todos</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="completed-todos">0</div>
                        <div>Completed</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="pending-todos">0</div>
                        <div>Pending</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="sync-pending">0</div>
                        <div>Sync Queue</div>
                    </div>
                </div>
            </div>

            <!-- PWA Controls -->
            <div class="row center-align" style="margin-top: 20px;">
                <div class="col s12">
                    <button id="sync-button" class="btn waves-effect waves-light blue" onclick="forceSyncData()">
                        <i class="material-icons left">sync</i>Sync Now
                    </button>
                    <button id="clear-cache-button" class="btn waves-effect waves-light orange" onclick="clearOfflineData()">
                        <i class="material-icons left">clear_all</i>Clear Offline Data
                    </button>
                </div>
            </div>
        </section>

        <!-- Add Todo Section -->
        <section id="add-section" class="section">
            <h4 class="center-align">
                <i class="material-icons large">add</i> Add New Todo
            </h4>
            <div class="row">
                <div class="col s12 m8 offset-m2 l6 offset-l3">
                    <div class="card">
                        <div class="card-content">
                            <form id="add-todo-form">
                                <div class="input-field">
                                    <i class="material-icons prefix">edit</i>
                                    <input type="text" id="new-task" required>
                                    <label for="new-task">Task Description</label>
                                </div>
                                <div class="input-field">
                                    <label>
                                        <input type="checkbox" id="new-completed">
                                        <span>Mark as completed</span>
                                    </label>
                                </div>
                                <div class="center-align">
                                    <button type="submit" class="btn waves-effect waves-light green">
                                        <i class="material-icons left">add</i>Add Todo
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Todos List Section -->
        <section id="todos-section" class="section">
            <h4 class="center-align">
                <i class="material-icons large">list</i> Todos List
            </h4>

            <!-- Filter Controls -->
            <div class="row">
                <div class="col s12 center-align">
                    <div class="chip" onclick="filterTodos('all')" id="filter-all">
                        All <span class="badge" id="count-all">0</span>
                    </div>
                    <div class="chip" onclick="filterTodos('pending')" id="filter-pending">
                        Pending <span class="badge" id="count-pending">0</span>
                    </div>
                    <div class="chip" onclick="filterTodos('completed')" id="filter-completed">
                        Completed <span class="badge" id="count-completed">0</span>
                    </div>
                </div>
            </div>

            <!-- Todos Container -->
            <div id="todos-container">
                <div class="center-align">
                    <div class="preloader-wrapper big active">
                        <div class="spinner-layer spinner-blue-only">
                            <div class="circle-clipper left">
                                <div class="circle"></div>
                            </div>
                            <div class="gap-patch">
                                <div class="circle"></div>
                            </div>
                            <div class="circle-clipper right">
                                <div class="circle"></div>
                            </div>
                        </div>
                    </div>
                    <p>Loading todos...</p>
                </div>
            </div>
        </section>
    </div>

    <!-- Materialize JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ftygxwlkokwgscyflanu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0eWd4d2xrb2t3Z3NjeWZsYW51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0NjQ0MjIsImV4cCI6MjA3NDA0MDQyMn0.ChWAPE-f5JFe59emOzN7rB0fl3rh74jlZsgtPwIpo48';

        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // PWA Variables
        let todoDB = null;
        let syncManager = null;
        let allTodos = [];
        let currentFilter = 'all';
        let editingTodoId = null;
                        <div class="stat-number" id="completion-rate">0%</div>
                        <div>Completion Rate</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Add New Todo Section -->
        <section id="add-section" class="section">
            <div class="card">
                <div class="card-content">
                    <span class="card-title">
                        <i class="material-icons left">add</i>Add New Todo
                    </span>
                    <form id="add-todo-form">
                        <div class="row">
                            <div class="input-field col s12">
                                <textarea id="new-task" class="materialize-textarea" required></textarea>
                                <label for="new-task">Task Description</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s12">
                                <label>
                                    <input type="checkbox" id="new-completed" />
                                    <span>Mark as completed</span>
                                </label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s12">
                                <button type="submit" class="btn waves-effect waves-light blue">
                                    <i class="material-icons left">add</i>Add Todo
                                </button>
                                <button type="button" class="btn waves-effect waves-light grey" onclick="clearForm()">
                                    <i class="material-icons left">clear</i>Clear
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Filter Section -->
        <section class="section">
            <div class="card">
                <div class="card-content">
                    <span class="card-title">
                        <i class="material-icons left">filter_list</i>Filter & Actions
                    </span>

                    <div class="row">
                        <div class="col s12">
                            <h6>🔍 Filter Todos</h6>
                            <div class="chip-container">
                                <div class="chip filter-chip active" onclick="filterTodos('all')" data-filter="all">
                                    All
                                </div>
                                <div class="chip filter-chip" onclick="filterTodos('pending')" data-filter="pending">
                                    Pending
                                </div>
                                <div class="chip filter-chip" onclick="filterTodos('completed')" data-filter="completed">
                                    Completed
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="divider" style="margin: 20px 0;"></div>

                    <div class="row">
                        <div class="col s12">
                            <h6>⚡ Bulk Actions</h6>
                            <button class="btn waves-effect waves-light green" onclick="markAllCompleted()">
                                <i class="material-icons left">done_all</i>Complete All
                            </button>
                            <button class="btn waves-effect waves-light red" onclick="deleteCompleted()">
                                <i class="material-icons left">delete</i>Delete Completed
                            </button>
                            <button class="btn waves-effect waves-light orange" onclick="exportTodos()">
                                <i class="material-icons left">file_download</i>Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Todos List Section -->
        <section id="todos-section" class="section">
            <div class="card">
                <div class="card-content">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s12 m8">
                            <span class="card-title">
                                <i class="material-icons left">list</i>Your Todos
                            </span>
                        </div>
                        <div class="col s12 m4 right-align">
                            <button class="btn waves-effect waves-light blue" onclick="loadTodos()">
                                <i class="material-icons left">refresh</i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <div id="todos-container">
                    <div class="center-align" style="padding: 40px;">
                        <div class="preloader-wrapper active">
                            <div class="spinner-layer spinner-blue-only">
                                <div class="circle-clipper left">
                                    <div class="circle"></div>
                                </div>
                                <div class="gap-patch">
                                    <div class="circle"></div>
                                </div>
                                <div class="circle-clipper right">
                                    <div class="circle"></div>
                                </div>
                            </div>
                        </div>
                        <p>Loading todos...</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Materialize JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ftygxwlkokwgscyflanu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0eWd4d2xrb2t3Z3NjeWZsYW51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0NjQ0MjIsImV4cCI6MjA3NDA0MDQyMn0.ChWAPE-f5JFe59emOzN7rB0fl3rh74jlZsgtPwIpo48';

        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // PWA Variables
        let todoDB = null;
        let syncManager = null;
        let allTodos = [];
        let currentFilter = 'all';
        let editingTodoId = null;

        // Initialize app
        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize Materialize components
            M.Sidenav.init(document.querySelectorAll('.sidenav'));
            M.Tooltip.init(document.querySelectorAll('.tooltipped'));
            M.FormSelect.init(document.querySelectorAll('select'));

            // Initialize PWA components
            await initializePWA();

            // Test connection and load todos
            await testConnection();
            setupFormHandler();
        });

        // Initialize PWA components
        async function initializePWA() {
            try {
                // Register service worker
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('Service Worker registered:', registration);
                }

                // Initialize IndexedDB
                todoDB = new TodoDB();
                await todoDB.init();
                console.log('IndexedDB initialized');

                // Initialize sync manager
                syncManager = new SyncManager(supabase, todoDB);
                console.log('Sync manager initialized');

                // Load offline todos first
                await loadOfflineTodos();

            } catch (error) {
                console.error('PWA initialization error:', error);
                showAlert('⚠️ Offline features may not work properly', 'warning');
            }
        }

        // Test connection and load todos
        async function testConnection() {
            try {
                updateConnectionStatus('connecting');
                showAlert('Connecting to Supabase...', 'info');
                
                // Test connection by trying to fetch todos
                const { data, error } = await supabase
                    .from('todos')
                    .select('*')
                    .limit(1);

                if (error && error.code === 'PGRST116') {
                    // Table doesn't exist
                    updateConnectionStatus('error');
                    showTableNotExistError();
                    return;
                }

                if (error) {
                    throw error;
                }

                updateConnectionStatus('connected');
                showAlert('✅ Connected to Supabase successfully!', 'success');
                loadTodos();
                
            } catch (error) {
                updateConnectionStatus('error');
                showAlert(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }

        // Update connection status
        function updateConnectionStatus(status) {
            const indicators = ['status-indicator', 'nav-status-indicator'];
            const texts = ['status-text', 'nav-status-text'];

            indicators.forEach(id => {
                const indicator = document.getElementById(id);
                if (indicator) {
                    indicator.className = 'status-indicator';
                    switch(status) {
                        case 'connecting':
                            indicator.classList.add('status-connecting');
                            break;
                        case 'connected':
                            indicator.classList.add('status-connected');
                            break;
                        case 'error':
                            indicator.classList.add('status-disconnected');
                            break;
                    }
                }
            });

            texts.forEach(id => {
                const text = document.getElementById(id);
                if (text) {
                    switch(status) {
                        case 'connecting':
                            text.textContent = 'Connecting...';
                            break;
                        case 'connected':
                            text.textContent = 'Connected';
                            break;
                        case 'error':
                            text.textContent = 'Connection Error';
                            break;
                    }
                }
            });
        }

        // Show table doesn't exist error
        function showTableNotExistError() {
            document.getElementById('todos-container').innerHTML = `
                <div class="empty-state">
                    <h3>📋 Todos Table Not Found</h3>
                    <p>The 'todos' table doesn't exist in your database yet.</p>
                    <p><strong>Please run this SQL in your Supabase SQL Editor:</strong></p>
                    <div style="background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left; font-family: monospace;">
-- Create todos table<br>
CREATE TABLE todos (<br>
&nbsp;&nbsp;&nbsp;&nbsp;id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,<br>
&nbsp;&nbsp;&nbsp;&nbsp;task TEXT NOT NULL,<br>
&nbsp;&nbsp;&nbsp;&nbsp;completed BOOLEAN DEFAULT FALSE,<br>
&nbsp;&nbsp;&nbsp;&nbsp;created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()<br>
);<br><br>
-- Enable Row Level Security<br>
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;<br><br>
-- Create policy to allow all operations<br>
CREATE POLICY "Allow all operations" ON todos FOR ALL USING (true);
                    </div>
                    <p>After running the SQL, refresh this page.</p>
                    <button class="btn btn-primary" onclick="location.reload()">🔄 Refresh Page</button>
                </div>
            `;
        }

        // Show alert messages
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'success' ? 'green lighten-4 green-text text-darken-2' :
                             type === 'error' ? 'red lighten-4 red-text text-darken-2' :
                             'blue lighten-4 blue-text text-darken-2';

            const icon = type === 'success' ? 'check_circle' :
                        type === 'error' ? 'error' : 'info';

            alertContainer.innerHTML = `
                <div class="card-panel ${alertClass}">
                    <i class="material-icons left">${icon}</i>
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // Setup form handler
        function setupFormHandler() {
            document.getElementById('add-todo-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await addTodo();
            });
        }

        // Load offline todos from IndexedDB
        async function loadOfflineTodos() {
            try {
                if (!todoDB) return;

                const offlineTodos = await todoDB.getAllTodos();
                allTodos = offlineTodos || [];
                updateStats();
                displayTodos();

                if (allTodos.length > 0) {
                    showAlert(`📱 Loaded ${allTodos.length} todos from offline storage`, 'info');
                }
            } catch (error) {
                console.error('Error loading offline todos:', error);
            }
        }

        // Load all todos (online with offline fallback)
        async function loadTodos() {
            try {
                if (navigator.onLine) {
                    showAlert('Loading todos...', 'info');

                    const { data, error } = await supabase
                        .from('todos')
                        .select('*')
                        .order('created_at', { ascending: false });

                    if (error) throw error;

                    allTodos = data || [];

                    // Update offline storage
                    if (todoDB) {
                        await todoDB.clearAllTodos();
                        for (const todo of allTodos) {
                            await todoDB.addTodo(todo);
                        }
                    }

                    updateStats();
                    displayTodos();
                    showAlert(`✅ Loaded ${allTodos.length} todos`, 'success');
                } else {
                    // Load from offline storage
                    await loadOfflineTodos();
                    showAlert('📱 Loaded todos from offline storage', 'info');
                }

            } catch (error) {
                showAlert(`❌ Error loading todos: ${error.message}`, 'error');
                console.error('Load error:', error);

                // Fallback to offline storage
                await loadOfflineTodos();
            }
        }

        // Add new todo (with offline support)
        async function addTodo() {
            try {
                const task = document.getElementById('new-task').value.trim();
                const completed = document.getElementById('new-completed').checked;

                if (!task) {
                    showAlert('Please enter a task description', 'error');
                    return;
                }

                const todoData = {
                    task: task,
                    completed: completed,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                if (navigator.onLine) {
                    // Online: Add to Supabase
                    showAlert('Adding todo...', 'info');

                    const { data, error } = await supabase
                        .from('todos')
                        .insert([todoData])
                        .select();

                    if (error) throw error;

                    // Add to offline storage
                    if (todoDB && data && data[0]) {
                        await todoDB.addTodo(data[0]);
                    }

                    showAlert('✅ Todo added successfully!', 'success');
                } else {
                    // Offline: Add to IndexedDB with temp ID
                    todoData.id = todoDB.generateTempId();

                    await todoDB.addTodo(todoData);
                    await todoDB.addToSyncQueue('create', todoData);

                    showAlert('📱 Todo saved offline. Will sync when online.', 'success');
                }

                clearForm();
                await loadOfflineTodos(); // Refresh display from local storage

            } catch (error) {
                showAlert(`❌ Error adding todo: ${error.message}`, 'error');
                console.error('Add error:', error);
            }
        }

        // Update todo (with offline support)
        async function updateTodo(id, updates) {
            try {
                updates.updated_at = new Date().toISOString();

                if (navigator.onLine && !todoDB.isTempId(id)) {
                    // Online: Update in Supabase
                    showAlert('Updating todo...', 'info');

                    const { data, error } = await supabase
                        .from('todos')
                        .update(updates)
                        .eq('id', id)
                        .select();

                    if (error) throw error;

                    // Update offline storage
                    if (todoDB && data && data[0]) {
                        await todoDB.updateTodo(data[0]);
                    }

                    showAlert('✅ Todo updated successfully!', 'success');
                } else {
                    // Offline or temp ID: Update in IndexedDB
                    const existingTodo = await todoDB.getTodo(id);
                    if (existingTodo) {
                        const updatedTodo = { ...existingTodo, ...updates };
                        await todoDB.updateTodo(updatedTodo);

                        if (!todoDB.isTempId(id)) {
                            await todoDB.addToSyncQueue('update', updates, id);
                        }
                    }

                    showAlert('📱 Todo updated offline. Will sync when online.', 'success');
                }

                await loadOfflineTodos(); // Refresh display

            } catch (error) {
                showAlert(`❌ Error updating todo: ${error.message}`, 'error');
                console.error('Update error:', error);
            }
        }

        // Delete todo (with offline support)
        async function deleteTodo(id) {
            if (!confirm('Are you sure you want to delete this todo?')) {
                return;
            }

            try {
                if (navigator.onLine && !todoDB.isTempId(id)) {
                    // Online: Delete from Supabase
                    showAlert('Deleting todo...', 'info');

                    const { error } = await supabase
                        .from('todos')
                        .delete()
                        .eq('id', id);

                    if (error) throw error;

                    // Remove from offline storage
                    if (todoDB) {
                        await todoDB.deleteTodo(id);
                    }

                    showAlert('✅ Todo deleted successfully!', 'success');
                } else {
                    // Offline or temp ID: Delete from IndexedDB
                    await todoDB.deleteTodo(id);

                    if (!todoDB.isTempId(id)) {
                        await todoDB.addToSyncQueue('delete', null, id);
                    }

                    showAlert('📱 Todo deleted offline. Will sync when online.', 'success');
                }

                await loadOfflineTodos(); // Refresh display

            } catch (error) {
                showAlert(`❌ Error deleting todo: ${error.message}`, 'error');
                console.error('Delete error:', error);
            }
        }

        // Toggle todo completion
        async function toggleTodo(id, currentStatus) {
            await updateTodo(id, { completed: !currentStatus });
        }

        // Clear form
        function clearForm() {
            document.getElementById('new-task').value = '';
            document.getElementById('new-completed').checked = false;
            cancelEdit();
        }

        // Display todos
        function displayTodos() {
            const container = document.getElementById('todos-container');

            let filteredTodos = allTodos;

            // Apply filter
            if (currentFilter === 'completed') {
                filteredTodos = allTodos.filter(todo => todo.completed);
            } else if (currentFilter === 'pending') {
                filteredTodos = allTodos.filter(todo => !todo.completed);
            }

            if (filteredTodos.length === 0) {
                container.innerHTML = `
                    <div class="center-align" style="padding: 60px 20px;">
                        <i class="material-icons large grey-text">assignment</i>
                        <h5>No Todos Found</h5>
                        <p class="grey-text">${currentFilter === 'all' ? 'You haven\'t added any todos yet.' :
                             currentFilter === 'completed' ? 'No completed todos.' : 'No pending todos.'}</p>
                        ${currentFilter === 'all' ? '<p class="grey-text">Add your first todo using the form above!</p>' : ''}
                    </div>
                `;
                return;
            }

            const todosHtml = filteredTodos.map(todo => `
                <div class="card todo-card ${todo.completed ? 'completed' : ''}" id="todo-${todo.id}">
                    <div class="card-content">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s12">
                                <span class="card-title ${todo.completed ? 'todo-task completed' : 'todo-task'}">
                                    ${escapeHtml(todo.task)}
                                </span>
                            </div>
                        </div>
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s12">
                                <div class="chip ${todo.completed ? 'green lighten-4 green-text' : 'orange lighten-4 orange-text'}">
                                    <i class="material-icons tiny">${todo.completed ? 'check_circle' : 'schedule'}</i>
                                    ${todo.completed ? 'Completed' : 'Pending'}
                                </div>
                                <span class="grey-text" style="margin-left: 10px; font-size: 0.9em;">
                                    Created: ${new Date(todo.created_at).toLocaleDateString()}
                                    ${new Date(todo.created_at).toLocaleTimeString()}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-action">
                        <button class="btn waves-effect waves-light ${todo.completed ? 'orange' : 'green'}"
                                onclick="toggleTodo(${todo.id}, ${todo.completed})">
                            <i class="material-icons left">${todo.completed ? 'undo' : 'check'}</i>
                            ${todo.completed ? 'Mark Pending' : 'Mark Complete'}
                        </button>
                        <button class="btn waves-effect waves-light blue" onclick="editTodo(${todo.id})">
                            <i class="material-icons left">edit</i>Edit
                        </button>
                        <button class="btn waves-effect waves-light red" onclick="deleteTodo(${todo.id})">
                            <i class="material-icons left">delete</i>Delete
                        </button>
                    </div>
                    <div id="edit-form-${todo.id}"></div>
                </div>
            `).join('');

            container.innerHTML = todosHtml;
        }

        // Update statistics
        function updateStats() {
            const total = allTodos.length;
            const completed = allTodos.filter(todo => todo.completed).length;
            const pending = total - completed;
            const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

            document.getElementById('total-todos').textContent = total;
            document.getElementById('completed-todos').textContent = completed;
            document.getElementById('pending-todos').textContent = pending;
            document.getElementById('completion-rate').textContent = `${completionRate}%`;
        }

        // Filter todos
        function filterTodos(filter) {
            currentFilter = filter;

            // Update filter chips
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
                if (chip.dataset.filter === filter) {
                    chip.classList.add('active');
                }
            });

            displayTodos();
        }

        // Edit todo
        function editTodo(id) {
            const todo = allTodos.find(t => t.id === id);
            if (!todo) return;

            // Cancel any existing edit
            cancelEdit();

            editingTodoId = id;

            const editFormHtml = `
                <div class="edit-form">
                    <h6><i class="material-icons left">edit</i>Edit Todo</h6>
                    <div class="row">
                        <div class="input-field col s12">
                            <textarea id="edit-task-${id}" class="materialize-textarea">${escapeHtml(todo.task)}</textarea>
                            <label for="edit-task-${id}" class="active">Task Description</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col s12">
                            <label>
                                <input type="checkbox" id="edit-completed-${id}" ${todo.completed ? 'checked' : ''} />
                                <span>Completed</span>
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col s12">
                            <button class="btn waves-effect waves-light green" onclick="saveEdit(${id})">
                                <i class="material-icons left">save</i>Save
                            </button>
                            <button class="btn waves-effect waves-light grey" onclick="cancelEdit()">
                                <i class="material-icons left">cancel</i>Cancel
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById(`edit-form-${id}`).innerHTML = editFormHtml;
        }

        // Save edit
        async function saveEdit(id) {
            const task = document.getElementById(`edit-task-${id}`).value.trim();
            const completed = document.getElementById(`edit-completed-${id}`).checked;

            if (!task) {
                showAlert('Please enter a task description', 'error');
                return;
            }

            await updateTodo(id, { task, completed });
            cancelEdit();
        }

        // Cancel edit
        function cancelEdit() {
            if (editingTodoId) {
                document.getElementById(`edit-form-${editingTodoId}`).innerHTML = '';
                editingTodoId = null;
            }
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Bulk operations
        async function markAllCompleted() {
            if (!confirm('Mark all todos as completed?')) return;

            try {
                showAlert('Updating all todos...', 'info');

                const { error } = await supabase
                    .from('todos')
                    .update({ completed: true })
                    .eq('completed', false);

                if (error) throw error;

                showAlert('✅ All todos marked as completed!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function deleteCompleted() {
            if (!confirm('Delete all completed todos? This cannot be undone.')) return;

            try {
                showAlert('Deleting completed todos...', 'info');

                const { error } = await supabase
                    .from('todos')
                    .delete()
                    .eq('completed', true);

                if (error) throw error;

                showAlert('✅ Completed todos deleted!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Export todos
        function exportTodos() {
            if (allTodos.length === 0) {
                showAlert('No todos to export', 'error');
                return;
            }

            const exportData = allTodos.map(todo => ({
                id: todo.id,
                task: todo.task,
                completed: todo.completed,
                created_at: todo.created_at
            }));

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `todos_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('✅ Todos exported successfully!', 'success');
        }

        // Auto-refresh every 30 seconds to sync with other users
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                loadTodos();
            }
        }, 30000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + Enter to add todo
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('add-todo-form').dispatchEvent(new Event('submit'));
            }

            // Escape to cancel edit
            if (e.key === 'Escape') {
                cancelEdit();
            }

            // Ctrl/Cmd + R to refresh (prevent default and use our function)
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                loadTodos();
            }
        });

        // Focus on task input when page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('new-task').focus();
            }, 1000);
        });

        // PWA Utility Functions
        async function forceSyncData() {
            if (!syncManager) {
                showAlert('❌ Sync manager not initialized', 'error');
                return;
            }

            if (!navigator.onLine) {
                showAlert('❌ Cannot sync while offline', 'error');
                return;
            }

            await syncManager.forcSync();
        }

        async function clearOfflineData() {
            if (!confirm('Are you sure you want to clear all offline data? This cannot be undone.')) {
                return;
            }

            try {
                if (todoDB) {
                    await todoDB.clearAllTodos();
                    await todoDB.clearSyncQueue();
                }

                // Clear service worker cache
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                }

                allTodos = [];
                updateStats();
                displayTodos();

                showAlert('✅ Offline data cleared successfully', 'success');
            } catch (error) {
                console.error('Error clearing offline data:', error);
                showAlert('❌ Error clearing offline data', 'error');
            }
        }

        // Update stats to include sync queue
        async function updateStats() {
            const total = allTodos.length;
            const completed = allTodos.filter(todo => todo.completed).length;
            const pending = total - completed;

            document.getElementById('total-todos').textContent = total;
            document.getElementById('completed-todos').textContent = completed;
            document.getElementById('pending-todos').textContent = pending;

            // Update sync queue count
            if (todoDB) {
                try {
                    const syncQueue = await todoDB.getSyncQueue();
                    document.getElementById('sync-pending').textContent = syncQueue.length;
                } catch (error) {
                    console.error('Error getting sync queue:', error);
                    document.getElementById('sync-pending').textContent = '?';
                }
            }

            // Update filter counts
            document.getElementById('count-all').textContent = total;
            document.getElementById('count-pending').textContent = pending;
            document.getElementById('count-completed').textContent = completed;
        }

        // Make loadTodos globally accessible for sync manager
        window.loadTodos = loadTodos;
    </script>
</body>
</html>
