<?php
// list_databases.php - List Supabase databases and projects
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['project_url']) || !isset($input['api_key'])) {
        echo json_encode(['error' => 'Project URL and API Key are required']);
        exit;
    }
    
    $project_url = rtrim($input['project_url'], '/');
    $api_key = $input['api_key'];
    
    try {
        // Test basic connection
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    "apikey: $api_key",
                    "Authorization: Bearer $api_key",
                    "Content-Type: application/json"
                ],
                'timeout' => 10
            ]
        ]);
        
        // Get basic project info
        $response = file_get_contents("$project_url/rest/v1/", false, $context);
        
        if ($response === false) {
            throw new Exception('Failed to connect to Supabase API');
        }
        
        $result = [
            'success' => true,
            'project_url' => $project_url,
            'connection_status' => 'Connected',
            'project_info' => []
        ];
        
        // Extract project ID from URL
        if (preg_match('/https:\/\/([^.]+)\.supabase\.co/', $project_url, $matches)) {
            $project_id = $matches[1];
            $result['project_info']['project_id'] = $project_id;
            $result['project_info']['region'] = 'Default';
        }
        
        // Try to get table information using information_schema
        $tables_query = "$project_url/rest/v1/rpc/get_tables";
        
        // Alternative: Try to access a common system table
        $schema_context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    "apikey: $api_key",
                    "Authorization: Bearer $api_key",
                    "Content-Type: application/json",
                    "Accept: application/json"
                ],
                'timeout' => 10
            ]
        ]);
        
        // Try to get schema information
        $tables_response = @file_get_contents("$project_url/rest/v1/information_schema.tables?select=table_name,table_schema&table_schema=eq.public", false, $schema_context);
        
        if ($tables_response) {
            $tables = json_decode($tables_response, true);
            $result['tables'] = $tables;
        } else {
            $result['tables'] = [];
            $result['note'] = 'Could not retrieve table information. This might be due to RLS policies or permissions.';
        }
        
        // Generate connection strings
        if (isset($project_id)) {
            $result['connection_strings'] = [
                'direct' => "postgresql://postgres:[YOUR-PASSWORD]@db.$project_id.supabase.co:5432/postgres",
                'pooled_session' => "postgres://postgres.$project_id:[YOUR-PASSWORD]@aws-0-us-east-1.pooler.supabase.com:5432/postgres",
                'pooled_transaction' => "postgres://postgres.$project_id:[YOUR-PASSWORD]@aws-0-us-east-1.pooler.supabase.com:6543/postgres"
            ];
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        echo json_encode([
            'error' => 'Connection failed: ' . $e->getMessage(),
            'success' => false
        ]);
    }
    
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Database Lister</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], input[type="password"], input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #10b981;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .results-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #10b981;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            background: #f9fafb;
            padding: 10px;
            border-radius: 6px;
        }

        .info-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .info-value {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            word-break: break-all;
        }

        .tables-list {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-item {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-item:last-child {
            border-bottom: none;
        }

        .table-name {
            font-weight: 600;
            color: #374151;
        }

        .table-schema {
            color: #6b7280;
            font-size: 14px;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Supabase Database Lister</h1>
            <p>List and explore your Supabase databases and tables</p>
        </div>
        
        <div class="content">
            <div id="alert-container"></div>
            
            <div class="form-section">
                <h2>🔗 Connect to Your Supabase Project</h2>
                <form id="connection-form">
                    <div class="form-group">
                        <label for="project-url">Project URL *</label>
                        <input type="url" id="project-url" placeholder="https://your-project.supabase.co" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="api-key">API Key (anon/public) *</label>
                        <input type="password" id="api-key" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">📋 List Databases & Tables</button>
                </form>
            </div>
            
            <div id="results-section" class="results-section" style="display: none;">
                <h2>📊 Database Information</h2>
                <div id="results-content"></div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('connection-form');
        const alertContainer = document.getElementById('alert-container');
        const resultsSection = document.getElementById('results-section');
        const resultsContent = document.getElementById('results-content');

        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            await listDatabases();
        });

        async function listDatabases() {
            const projectUrl = document.getElementById('project-url').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();

            if (!projectUrl || !apiKey) {
                showAlert('Please provide both Project URL and API Key', 'error');
                return;
            }

            showAlert('Connecting and listing databases...', 'info');
            resultsSection.style.display = 'none';

            try {
                const response = await fetch('list_databases.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        project_url: projectUrl,
                        api_key: apiKey
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('✅ Successfully connected to Supabase!', 'success');
                    displayResults(result);
                } else {
                    showAlert(`❌ ${result.error}`, 'error');
                }

            } catch (error) {
                showAlert(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }

        function displayResults(data) {
            let html = `
                <div class="info-card">
                    <h3>🎯 Project Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Project ID</div>
                            <div class="info-value">${data.project_info.project_id || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Status</div>
                            <div class="info-value">${data.connection_status}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Project URL</div>
                            <div class="info-value">${data.project_url}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Region</div>
                            <div class="info-value">${data.project_info.region || 'Default'}</div>
                        </div>
                    </div>
                </div>
            `;

            if (data.tables && data.tables.length > 0) {
                html += `
                    <div class="info-card">
                        <h3>📋 Database Tables (${data.tables.length})</h3>
                        <div class="tables-list">
                `;
                
                data.tables.forEach(table => {
                    html += `
                        <div class="table-item">
                            <div>
                                <div class="table-name">${table.table_name}</div>
                                <div class="table-schema">Schema: ${table.table_schema}</div>
                            </div>
                        </div>
                    `;
                });
                
                html += `</div></div>`;
            } else {
                html += `
                    <div class="info-card">
                        <h3>📋 Database Tables</h3>
                        <p>No tables found or unable to retrieve table information.</p>
                        ${data.note ? `<p><em>${data.note}</em></p>` : ''}
                    </div>
                `;
            }

            if (data.connection_strings) {
                html += `
                    <div class="info-card">
                        <h3>🔗 Connection Strings</h3>
                        <p><strong>Direct Connection:</strong></p>
                        <div class="code-block">${data.connection_strings.direct}</div>
                        
                        <p><strong>Pooled Connection (Session Mode):</strong></p>
                        <div class="code-block">${data.connection_strings.pooled_session}</div>
                        
                        <p><strong>Pooled Connection (Transaction Mode):</strong></p>
                        <div class="code-block">${data.connection_strings.pooled_transaction}</div>
                    </div>
                `;
            }

            resultsContent.innerHTML = html;
            resultsSection.style.display = 'block';
        }

        function showAlert(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 
                             type === 'error' ? 'alert-error' : 'alert-info';
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
