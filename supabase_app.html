<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Database Explorer PWA</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="DB Explorer PWA">
    <meta name="description" content="A Progressive Web App for exploring Supabase databases">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- <PERSON><PERSON> Scripts -->
    <script src="/js/pwa-installer.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 30px;
        }

        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            color: #6b7280;
            transition: all 0.3s;
        }

        .tab.active {
            color: #10b981;
            border-bottom: 3px solid #10b981;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], input[type="email"], input[type="number"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #10b981;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-success {
            background: #22c55e;
            color: white;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .data-table th {
            background: #374151;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #22c55e;
        }

        .status-disconnected {
            background: #ef4444;
        }

        .connection-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Supabase JavaScript App</h1>
            <p>Connected to: ftygxwlkokwgscyflanu.supabase.co</p>
            <div>
                <span class="status-indicator" id="connection-status"></span>
                <span id="connection-text">Connecting...</span>
            </div>
        </div>
        
        <div class="content">
            <div id="alert-container"></div>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('connection')">🔗 Connection</button>
                <button class="tab" onclick="showTab('tables')">📋 Tables</button>
                <button class="tab" onclick="showTab('crud')">✏️ CRUD Operations</button>
                <button class="tab" onclick="showTab('realtime')">⚡ Real-time</button>
            </div>

            <!-- Connection Tab -->
            <div id="connection" class="tab-content active">
                <div class="connection-info">
                    <h3>📡 Connection Details</h3>
                    <div class="form-row">
                        <div>
                            <strong>Project URL:</strong><br>
                            <code>https://ftygxwlkokwgscyflanu.supabase.co</code>
                        </div>
                        <div>
                            <strong>Project ID:</strong><br>
                            <code>ftygxwlkokwgscyflanu</code>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>🔧 Test Connection</h3>
                    <button class="btn btn-primary" onclick="testConnection()">Test Connection</button>
                    <button class="btn btn-secondary" onclick="getProjectInfo()">Get Project Info</button>
                    <button class="btn btn-success" onclick="listTables()">List Tables</button>
                </div>

                <div id="connection-results"></div>
            </div>

            <!-- Tables Tab -->
            <div id="tables" class="tab-content">
                <div class="form-section">
                    <h3>📋 Database Tables</h3>
                    <button class="btn btn-primary" onclick="loadTables()">Refresh Tables</button>
                    <button class="btn btn-secondary" onclick="createSampleTable()">Create Sample Table</button>
                </div>
                
                <div id="tables-container">
                    <div class="loading">Click "Refresh Tables" to load your database tables...</div>
                </div>
            </div>

            <!-- CRUD Tab -->
            <div id="crud" class="tab-content">
                <div class="form-section">
                    <h3>✏️ CRUD Operations</h3>
                    <p>Select a table to perform CRUD operations:</p>
                    <select id="table-select" onchange="loadTableData()">
                        <option value="">Select a table...</option>
                    </select>
                    <button class="btn btn-primary" onclick="loadTableData()">Load Data</button>
                </div>

                <div class="form-section" id="insert-form" style="display: none;">
                    <h4>➕ Insert New Record</h4>
                    <div id="insert-fields"></div>
                    <button class="btn btn-success" onclick="insertRecord()">Insert Record</button>
                </div>

                <div id="crud-data-container"></div>
            </div>

            <!-- Real-time Tab -->
            <div id="realtime" class="tab-content">
                <div class="form-section">
                    <h3>⚡ Real-time Subscriptions</h3>
                    <p>Monitor real-time changes to your database:</p>
                    <select id="realtime-table-select">
                        <option value="">Select a table to monitor...</option>
                    </select>
                    <button class="btn btn-primary" onclick="startRealtimeSubscription()">Start Monitoring</button>
                    <button class="btn btn-danger" onclick="stopRealtimeSubscription()">Stop Monitoring</button>
                </div>

                <div id="realtime-logs" class="form-section">
                    <h4>📊 Real-time Events</h4>
                    <div id="realtime-events" style="max-height: 400px; overflow-y: auto; background: #f8fafc; padding: 15px; border-radius: 8px;">
                        <p>No events yet. Start monitoring a table to see real-time changes.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ftygxwlkokwgscyflanu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0eWd4d2xrb2t3Z3NjeWZsYW51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0NjQ0MjIsImV4cCI6MjA3NDA0MDQyMn0.ChWAPE-f5JFe59emOzN7rB0fl3rh74jlZsgtPwIpo48';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        let realtimeSubscription = null;
        let availableTables = [];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            testConnection();
        });

        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Test connection
        async function testConnection() {
            try {
                showAlert('Testing connection...', 'info');

                // Test connection by making a simple API call to the REST endpoint
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    method: 'GET',
                    headers: {
                        'apikey': supabaseKey,
                        'Authorization': `Bearer ${supabaseKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    updateConnectionStatus(true);
                    showAlert('✅ Successfully connected to Supabase!', 'success');

                    // Try to load available tables automatically
                    setTimeout(() => {
                        loadTables();
                    }, 1000);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                updateConnectionStatus(false);
                showAlert(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connection-status');
            const statusText = document.getElementById('connection-text');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'Connected';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Disconnected';
            }
        }

        // Show alert messages
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'success' ? 'alert-success' :
                             type === 'error' ? 'alert-error' : 'alert-info';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // Get project information
        async function getProjectInfo() {
            try {
                showAlert('Getting project information...', 'info');

                const resultsDiv = document.getElementById('connection-results');
                resultsDiv.innerHTML = `
                    <div class="form-section">
                        <h3>📊 Project Information</h3>
                        <div class="code-block">
                            Project URL: ${supabaseUrl}
                            Project ID: ftygxwlkokwgscyflanu
                            Region: ap-southeast-1
                            API Key: ${supabaseKey.substring(0, 20)}...
                        </div>
                    </div>
                `;

                showAlert('✅ Project information loaded', 'success');
            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // List tables
        async function listTables() {
            try {
                showAlert('Loading tables...', 'info');

                // Test the API endpoint and show available information
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    headers: {
                        'apikey': supabaseKey,
                        'Authorization': `Bearer ${supabaseKey}`
                    }
                });

                if (response.ok) {
                    const resultsDiv = document.getElementById('connection-results');
                    resultsDiv.innerHTML = `
                        <div class="form-section">
                            <h3>📋 Supabase REST API Status</h3>
                            <div class="alert alert-success">
                                ✅ Your Supabase REST API is accessible and working!
                            </div>
                            <p><strong>Next steps:</strong></p>
                            <ul>
                                <li>Go to the <strong>Tables</strong> tab to explore your database</li>
                                <li>Use <strong>CRUD Operations</strong> to manage your data</li>
                                <li>Try <strong>Real-time</strong> features for live updates</li>
                            </ul>
                            <div class="code-block">
                                API Endpoint: ${supabaseUrl}/rest/v1/
                                Status: Connected ✅
                                Authentication: Working ✅
                            </div>
                        </div>
                    `;
                    showAlert('✅ API is accessible and ready to use!', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                const resultsDiv = document.getElementById('connection-results');
                resultsDiv.innerHTML = `
                    <div class="form-section">
                        <h3>❌ Connection Error</h3>
                        <div class="alert alert-error">
                            Error: ${error.message}
                        </div>
                    </div>
                `;
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Load tables for the tables tab
        async function loadTables() {
            try {
                showAlert('Scanning for database tables...', 'info');

                const container = document.getElementById('tables-container');
                container.innerHTML = '<div class="loading">Scanning for tables...</div>';

                // Try to get a list of tables by attempting to query common table names
                const commonTables = [
                    'users', 'profiles', 'posts', 'products', 'orders', 'todos',
                    'customers', 'items', 'categories', 'comments', 'messages',
                    'tasks', 'projects', 'employees', 'invoices', 'payments'
                ];

                const existingTables = [];
                const tableChecks = [];

                // Check tables in parallel for better performance
                for (const tableName of commonTables) {
                    tableChecks.push(
                        supabase
                            .from(tableName)
                            .select('*')
                            .limit(1)
                            .then(({ data, error }) => {
                                if (!error) {
                                    return tableName;
                                }
                                return null;
                            })
                            .catch(() => null)
                    );
                }

                const results = await Promise.all(tableChecks);
                results.forEach(tableName => {
                    if (tableName) {
                        existingTables.push(tableName);
                    }
                });

                availableTables = existingTables;
                updateTableSelects();

                if (existingTables.length > 0) {
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📋 Available Tables (${existingTables.length})</h3>
                            <div class="alert alert-success">
                                Found ${existingTables.length} accessible table(s) in your database!
                            </div>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Table Name</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${existingTables.map(table => `
                                        <tr>
                                            <td><strong>${table}</strong></td>
                                            <td>
                                                <button class="btn btn-primary" onclick="viewTableData('${table}')">View Data</button>
                                                <button class="btn btn-success" onclick="selectTableForCrud('${table}')">Use in CRUD</button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                    showAlert(`✅ Found ${existingTables.length} accessible tables`, 'success');
                } else {
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📋 No Tables Found</h3>
                            <div class="alert alert-info">
                                No accessible tables found in your database.
                            </div>
                            <p><strong>This could mean:</strong></p>
                            <ul>
                                <li>🔧 No tables exist in your database yet</li>
                                <li>🔒 Tables exist but RLS policies prevent access</li>
                                <li>📝 Tables have different names than the common ones we checked</li>
                                <li>🔑 API key doesn't have sufficient permissions</li>
                            </ul>
                            <p><strong>What you can do:</strong></p>
                            <button class="btn btn-primary" onclick="createSampleTable()">📋 Create Sample Table</button>
                            <button class="btn btn-secondary" onclick="showCustomTableTest()">🔍 Test Custom Table Name</button>
                        </div>
                    `;
                    showAlert('⚠️ No accessible tables found - try creating a sample table', 'info');
                }

            } catch (error) {
                document.getElementById('tables-container').innerHTML = `
                    <div class="form-section">
                        <h3>❌ Error Loading Tables</h3>
                        <div class="alert alert-error">
                            Error: ${error.message}
                        </div>
                        <p>There was an issue scanning your database. Please check your connection and try again.</p>
                    </div>
                `;
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Update table select dropdowns
        function updateTableSelects() {
            const tableSelect = document.getElementById('table-select');
            const realtimeTableSelect = document.getElementById('realtime-table-select');

            const options = availableTables.map(table =>
                `<option value="${table}">${table}</option>`
            ).join('');

            tableSelect.innerHTML = '<option value="">Select a table...</option>' + options;
            realtimeTableSelect.innerHTML = '<option value="">Select a table to monitor...</option>' + options;
        }

        // Create sample table
        async function createSampleTable() {
            try {
                showAlert('Creating sample table...', 'info');

                // Note: This requires database admin access
                // For demo purposes, we'll show what the SQL would look like
                const sqlCode = `-- Create a sample todos table
CREATE TABLE todos (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    task TEXT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now
CREATE POLICY "Allow all operations" ON todos FOR ALL USING (true);`;

                document.getElementById('tables-container').innerHTML = `
                    <div class="form-section">
                        <h3>🔧 Create Sample Table</h3>
                        <div class="alert alert-info">
                            To create a sample table, copy and run this SQL in your Supabase SQL Editor:
                        </div>
                        <div class="code-block">${sqlCode}</div>
                        <p><strong>Steps:</strong></p>
                        <ol>
                            <li>Go to your Supabase Dashboard</li>
                            <li>Navigate to SQL Editor</li>
                            <li>Copy and paste the SQL above</li>
                            <li>Click "Run" to execute</li>
                            <li>Come back here and click "Refresh Tables"</li>
                        </ol>
                        <button class="btn btn-primary" onclick="loadTables()">🔄 Refresh Tables</button>
                        <button class="btn btn-secondary" onclick="showCustomTableTest()">🔍 Test Custom Table</button>
                    </div>
                `;

                showAlert('📋 Sample table SQL generated. Run it in Supabase SQL Editor.', 'info');

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Show custom table test form
        function showCustomTableTest() {
            document.getElementById('tables-container').innerHTML = `
                <div class="form-section">
                    <h3>🔍 Test Custom Table Name</h3>
                    <p>If you have tables with different names, test them here:</p>
                    <div class="form-group">
                        <label for="custom-table-name">Table Name:</label>
                        <input type="text" id="custom-table-name" placeholder="Enter table name (e.g., my_table)">
                    </div>
                    <button class="btn btn-primary" onclick="testCustomTable()">Test Table</button>
                    <button class="btn btn-secondary" onclick="loadTables()">Back to Scan</button>
                </div>
                <div id="custom-table-results"></div>
            `;
        }

        // Test custom table name
        async function testCustomTable() {
            const tableName = document.getElementById('custom-table-name').value.trim();
            if (!tableName) {
                showAlert('Please enter a table name', 'error');
                return;
            }

            try {
                showAlert(`Testing table "${tableName}"...`, 'info');

                const { data, error } = await supabase
                    .from(tableName)
                    .select('*')
                    .limit(1);

                const resultsDiv = document.getElementById('custom-table-results');

                if (error) {
                    resultsDiv.innerHTML = `
                        <div class="form-section">
                            <h4>❌ Table "${tableName}" not accessible</h4>
                            <div class="alert alert-error">
                                Error: ${error.message}
                            </div>
                            <p>This could mean:</p>
                            <ul>
                                <li>Table doesn't exist</li>
                                <li>No permission to access it</li>
                                <li>RLS policies prevent access</li>
                            </ul>
                        </div>
                    `;
                    showAlert(`❌ Table "${tableName}" not accessible`, 'error');
                } else {
                    availableTables.push(tableName);
                    updateTableSelects();

                    resultsDiv.innerHTML = `
                        <div class="form-section">
                            <h4>✅ Table "${tableName}" found!</h4>
                            <div class="alert alert-success">
                                Successfully accessed table "${tableName}"
                            </div>
                            <p>Found ${data.length} record(s) in the table.</p>
                            <button class="btn btn-primary" onclick="viewTableData('${tableName}')">View Data</button>
                            <button class="btn btn-success" onclick="selectTableForCrud('${tableName}')">Use in CRUD</button>
                        </div>
                    `;
                    showAlert(`✅ Table "${tableName}" is accessible!`, 'success');
                }

            } catch (error) {
                showAlert(`❌ Error testing table: ${error.message}`, 'error');
            }
        }

        // Select table for CRUD operations
        function selectTableForCrud(tableName) {
            // Switch to CRUD tab
            showTab('crud');

            // Set the table in the dropdown
            document.getElementById('table-select').value = tableName;

            // Load the table data
            loadTableData();

            showAlert(`Switched to CRUD operations for table "${tableName}"`, 'success');
        }

        // View table data
        async function viewTableData(tableName) {
            try {
                showAlert(`Loading data from ${tableName}...`, 'info');

                const { data, error } = await supabase
                    .from(tableName)
                    .select('*')
                    .limit(10);

                if (error) throw error;

                const container = document.getElementById('tables-container');

                if (data.length > 0) {
                    const columns = Object.keys(data[0]);
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📊 Data from "${tableName}" (${data.length} rows)</h3>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        ${columns.map(col => `<th>${col}</th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.map(row => `
                                        <tr>
                                            ${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                            <button class="btn btn-secondary" onclick="loadTables()">Back to Tables</button>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📊 Table "${tableName}" is empty</h3>
                            <p>No data found in this table.</p>
                            <button class="btn btn-secondary" onclick="loadTables()">Back to Tables</button>
                        </div>
                    `;
                }

                showAlert(`✅ Loaded data from ${tableName}`, 'success');

            } catch (error) {
                showAlert(`❌ Error loading data: ${error.message}`, 'error');
            }
        }

        // Load table data for CRUD operations
        async function loadTableData() {
            const tableName = document.getElementById('table-select').value;
            if (!tableName) {
                showAlert('Please select a table first', 'error');
                return;
            }

            try {
                showAlert(`Loading ${tableName} data...`, 'info');

                const { data, error } = await supabase
                    .from(tableName)
                    .select('*')
                    .limit(20);

                if (error) throw error;

                // Show insert form
                document.getElementById('insert-form').style.display = 'block';
                generateInsertForm(tableName, data[0] || {});

                // Display data
                const container = document.getElementById('crud-data-container');

                if (data.length > 0) {
                    const columns = Object.keys(data[0]);
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📊 ${tableName} Data (${data.length} records)</h3>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        ${columns.map(col => `<th>${col}</th>`).join('')}
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.map(row => `
                                        <tr>
                                            ${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}
                                            <td>
                                                <button class="btn btn-danger" onclick="deleteRecord('${tableName}', '${row.id || row[columns[0]]}')">Delete</button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="form-section">
                            <h3>📊 ${tableName} is empty</h3>
                            <p>No records found. Use the form above to add some data.</p>
                        </div>
                    `;
                }

                showAlert(`✅ Loaded ${data.length} records from ${tableName}`, 'success');

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Generate insert form based on table structure
        function generateInsertForm(tableName, sampleRow) {
            const fieldsContainer = document.getElementById('insert-fields');
            const columns = Object.keys(sampleRow);

            fieldsContainer.innerHTML = columns
                .filter(col => col !== 'id' && col !== 'created_at' && col !== 'updated_at')
                .map(col => `
                    <div class="form-group">
                        <label for="field-${col}">${col}:</label>
                        <input type="text" id="field-${col}" name="${col}" placeholder="Enter ${col}">
                    </div>
                `).join('');
        }

        // Insert new record
        async function insertRecord() {
            const tableName = document.getElementById('table-select').value;
            if (!tableName) {
                showAlert('Please select a table first', 'error');
                return;
            }

            try {
                const formData = {};
                const inputs = document.querySelectorAll('#insert-fields input');

                inputs.forEach(input => {
                    if (input.value.trim()) {
                        formData[input.name] = input.value.trim();
                    }
                });

                if (Object.keys(formData).length === 0) {
                    showAlert('Please fill in at least one field', 'error');
                    return;
                }

                showAlert('Inserting record...', 'info');

                const { data, error } = await supabase
                    .from(tableName)
                    .insert([formData])
                    .select();

                if (error) throw error;

                showAlert('✅ Record inserted successfully!', 'success');

                // Clear form
                inputs.forEach(input => input.value = '');

                // Reload data
                loadTableData();

            } catch (error) {
                showAlert(`❌ Error inserting record: ${error.message}`, 'error');
            }
        }

        // Delete record
        async function deleteRecord(tableName, recordId) {
            if (!confirm('Are you sure you want to delete this record?')) {
                return;
            }

            try {
                showAlert('Deleting record...', 'info');

                const { error } = await supabase
                    .from(tableName)
                    .delete()
                    .eq('id', recordId);

                if (error) throw error;

                showAlert('✅ Record deleted successfully!', 'success');
                loadTableData();

            } catch (error) {
                showAlert(`❌ Error deleting record: ${error.message}`, 'error');
            }
        }

        // Start real-time subscription
        async function startRealtimeSubscription() {
            const tableName = document.getElementById('realtime-table-select').value;
            if (!tableName) {
                showAlert('Please select a table to monitor', 'error');
                return;
            }

            try {
                // Stop existing subscription
                if (realtimeSubscription) {
                    realtimeSubscription.unsubscribe();
                }

                showAlert(`Starting real-time monitoring for ${tableName}...`, 'info');

                realtimeSubscription = supabase
                    .channel(`public:${tableName}`)
                    .on('postgres_changes',
                        {
                            event: '*',
                            schema: 'public',
                            table: tableName
                        },
                        (payload) => {
                            addRealtimeEvent(payload);
                        }
                    )
                    .subscribe();

                showAlert(`✅ Real-time monitoring started for ${tableName}`, 'success');

            } catch (error) {
                showAlert(`❌ Error starting real-time: ${error.message}`, 'error');
            }
        }

        // Stop real-time subscription
        function stopRealtimeSubscription() {
            if (realtimeSubscription) {
                realtimeSubscription.unsubscribe();
                realtimeSubscription = null;
                showAlert('✅ Real-time monitoring stopped', 'success');
            } else {
                showAlert('No active real-time subscription', 'error');
            }
        }

        // Add real-time event to log
        function addRealtimeEvent(payload) {
            const eventsContainer = document.getElementById('realtime-events');
            const timestamp = new Date().toLocaleTimeString();

            const eventHtml = `
                <div style="border-left: 4px solid #10b981; padding: 10px; margin: 10px 0; background: white; border-radius: 4px;">
                    <strong>${timestamp} - ${payload.eventType.toUpperCase()}</strong><br>
                    <small>Table: ${payload.table}</small><br>
                    <div class="code-block" style="margin-top: 5px; font-size: 12px;">
                        ${JSON.stringify(payload.new || payload.old || {}, null, 2)}
                    </div>
                </div>
            `;

            eventsContainer.innerHTML = eventHtml + eventsContainer.innerHTML;

            // Keep only last 10 events
            const events = eventsContainer.children;
            while (events.length > 10) {
                eventsContainer.removeChild(events[events.length - 1]);
            }
        }

        // Register service worker for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('Service Worker registered:', registration);
                } catch (error) {
                    console.error('Service Worker registration failed:', error);
                }
            });
        }
    </script>
</body>
</html>
