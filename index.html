<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Dashboard</title>
    
    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Supabase JS -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        .brand-logo {
            font-weight: 300;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero-section h1 {
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
        }
        
        .hero-section p {
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .feature-card {
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #4caf50;
        }
        
        .status-disconnected {
            background: #f44336;
        }
        
        .status-connecting {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .connection-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .stats-grid {
            margin: 40px 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 30px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .footer {
            background: #37474f;
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }
        
        .sidenav-trigger {
            color: white;
        }
        
        .nav-wrapper {
            padding: 0 20px;
        }
        
        @media only screen and (max-width: 992px) {
            .hero-section h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="blue darken-2">
        <div class="nav-wrapper">
            <a href="#" class="brand-logo">
                <i class="material-icons left">cloud</i>Supabase Dashboard
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="#connection-section">Connection</a></li>
                <li><a href="#features-section">Features</a></li>
                <li><a href="#stats-section">Statistics</a></li>
                <li>
                    <a href="#" id="connection-status-nav">
                        <span class="status-indicator status-connecting" id="nav-status-indicator"></span>
                        <span id="nav-status-text">Connecting...</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><div class="user-view blue darken-2">
            <div class="background">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 100%;"></div>
            </div>
            <a href="#"><span class="white-text name">Supabase Dashboard</span></a>
            <a href="#"><span class="white-text email">Database Management</span></a>
        </div></li>
        <li><a href="#connection-section"><i class="material-icons">wifi</i>Connection</a></li>
        <li><a href="#features-section"><i class="material-icons">apps</i>Features</a></li>
        <li><a href="#stats-section"><i class="material-icons">assessment</i>Statistics</a></li>
        <li><div class="divider"></div></li>
        <li><a href="todos_crud.html"><i class="material-icons">check_box</i>Todos CRUD</a></li>
        <li><a href="supabase_app.html"><i class="material-icons">explore</i>Database Explorer</a></li>
    </ul>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <h1>🚀 Supabase Dashboard</h1>
            <p>Complete database management and CRUD operations</p>
            <p>Connected to: <strong>ftygxwlkokwgscyflanu.supabase.co</strong></p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Alert Container -->
        <div id="alert-container" style="margin-top: 20px;"></div>

        <!-- Connection Section -->
        <section id="connection-section" class="section">
            <div class="row">
                <div class="col s12">
                    <h4 class="center-align">
                        <i class="material-icons large blue-text">wifi</i>
                        <br>Connection Status
                    </h4>
                </div>
            </div>
            
            <div class="connection-info">
                <h5>📡 Database Connection Details</h5>
                <div class="row">
                    <div class="col s12 m6">
                        <p><strong>Project URL:</strong><br>
                        <code>https://ftygxwlkokwgscyflanu.supabase.co</code></p>
                    </div>
                    <div class="col s12 m6">
                        <p><strong>Project ID:</strong><br>
                        <code>ftygxwlkokwgscyflanu</code></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col s12">
                        <p><strong>Region:</strong> ap-southeast-1</p>
                        <p><strong>Status:</strong> 
                            <span class="status-indicator" id="connection-indicator"></span>
                            <span id="connection-text">Testing connection...</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="center-align">
                <button class="btn waves-effect waves-light blue" onclick="testConnection()">
                    <i class="material-icons left">refresh</i>Test Connection
                </button>
                <button class="btn waves-effect waves-light green" onclick="loadDatabaseInfo()">
                    <i class="material-icons left">info</i>Database Info
                </button>
            </div>

            <div id="connection-results" style="margin-top: 20px;"></div>
        </section>

        <!-- Features Section -->
        <section id="features-section" class="section">
            <div class="row">
                <div class="col s12">
                    <h4 class="center-align">
                        <i class="material-icons large purple-text">apps</i>
                        <br>Available Features
                    </h4>
                </div>
            </div>

            <div class="row">
                <!-- Todos CRUD -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">check_box</i>
                            <span class="card-title">Todos CRUD</span>
                            <p>Complete todo management with create, read, update, and delete operations. Perfect for task management.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="todos_crud.html" class="btn waves-effect waves-light blue">
                                <i class="material-icons left">launch</i>Open Todos
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Database Explorer -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">explore</i>
                            <span class="card-title">Database Explorer</span>
                            <p>Explore your database tables, view data, and perform operations on any table in your Supabase database.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="supabase_app.html" class="btn waves-effect waves-light green">
                                <i class="material-icons left">launch</i>Explore Database
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Real-time Monitor -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">timeline</i>
                            <span class="card-title">Real-time Monitor</span>
                            <p>Monitor real-time changes to your database tables with live event streaming and notifications.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="supabase_app.html#realtime" class="btn waves-effect waves-light orange">
                                <i class="material-icons left">launch</i>Monitor Changes
                            </a>
                        </div>
                    </div>
                </div>

                <!-- CLI Tools -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">terminal</i>
                            <span class="card-title">CLI Tools</span>
                            <p>Command-line interface tools for managing your Supabase projects and executing database operations.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="cli_commands.php" class="btn waves-effect waves-light purple">
                                <i class="material-icons left">launch</i>CLI Tools
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Table Lister -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">list</i>
                            <span class="card-title">Table Lister</span>
                            <p>List and analyze all tables in your database with detailed information and quick access options.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="list_databases.php" class="btn waves-effect waves-light teal">
                                <i class="material-icons left">launch</i>List Tables
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Connection Checker -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <i class="material-icons feature-icon">wifi_tethering</i>
                            <span class="card-title">Connection Checker</span>
                            <p>Test and validate your Supabase connection with detailed diagnostics and troubleshooting tools.</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="supabase_checker.html" class="btn waves-effect waves-light red">
                                <i class="material-icons left">launch</i>Check Connection
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section id="stats-section" class="section">
            <div class="row">
                <div class="col s12">
                    <h4 class="center-align">
                        <i class="material-icons large green-text">assessment</i>
                        <br>Database Statistics
                    </h4>
                </div>
            </div>

            <div class="row stats-grid" id="stats-container">
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="total-tables">-</div>
                        <div>Total Tables</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="total-records">-</div>
                        <div>Total Records</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="api-status">-</div>
                        <div>API Status</div>
                    </div>
                </div>
                <div class="col s12 m6 l3">
                    <div class="stat-card">
                        <div class="stat-number" id="last-updated">-</div>
                        <div>Last Updated</div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col s12 m6">
                    <h5>Supabase Dashboard</h5>
                    <p>A comprehensive database management interface for Supabase projects.</p>
                </div>
                <div class="col s12 m6">
                    <h5>Quick Links</h5>
                    <ul>
                        <li><a href="https://supabase.com/dashboard" target="_blank" class="grey-text text-lighten-3">Supabase Dashboard</a></li>
                        <li><a href="https://supabase.com/docs" target="_blank" class="grey-text text-lighten-3">Documentation</a></li>
                        <li><a href="todos_crud.html" class="grey-text text-lighten-3">Todos CRUD</a></li>
                        <li><a href="supabase_app.html" class="grey-text text-lighten-3">Database Explorer</a></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div class="col s12 center-align">
                    <p class="grey-text text-lighten-4">© 2024 Supabase Dashboard. Built with Materialize CSS.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Materialize JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ftygxwlkokwgscyflanu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0eWd4d2xrb2t3Z3NjeWZsYW51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0NjQ0MjIsImV4cCI6MjA3NDA0MDQyMn0.ChWAPE-f5JFe59emOzN7rB0fl3rh74jlZsgtPwIpo48';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Initialize Materialize components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidenav
            var elems = document.querySelectorAll('.sidenav');
            M.Sidenav.init(elems);

            // Initialize tooltips
            var tooltips = document.querySelectorAll('.tooltipped');
            M.Tooltip.init(tooltips);

            // Test connection on load
            testConnection();

            // Load statistics
            loadDatabaseStats();
        });

        // Update connection status
        function updateConnectionStatus(status) {
            const indicators = ['connection-indicator', 'nav-status-indicator'];
            const texts = ['connection-text', 'nav-status-text'];

            indicators.forEach(id => {
                const indicator = document.getElementById(id);
                if (indicator) {
                    indicator.className = 'status-indicator';
                    switch(status) {
                        case 'connecting':
                            indicator.classList.add('status-connecting');
                            break;
                        case 'connected':
                            indicator.classList.add('status-connected');
                            break;
                        case 'error':
                            indicator.classList.add('status-disconnected');
                            break;
                    }
                }
            });

            texts.forEach(id => {
                const text = document.getElementById(id);
                if (text) {
                    switch(status) {
                        case 'connecting':
                            text.textContent = 'Connecting...';
                            break;
                        case 'connected':
                            text.textContent = 'Connected';
                            break;
                        case 'error':
                            text.textContent = 'Disconnected';
                            break;
                    }
                }
            });
        }

        // Show alert messages
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'success' ? 'green lighten-4 green-text text-darken-2' :
                             type === 'error' ? 'red lighten-4 red-text text-darken-2' :
                             'blue lighten-4 blue-text text-darken-2';

            const icon = type === 'success' ? 'check_circle' :
                        type === 'error' ? 'error' : 'info';

            alertContainer.innerHTML = `
                <div class="card-panel ${alertClass}">
                    <i class="material-icons left">${icon}</i>
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // Test connection
        async function testConnection() {
            try {
                updateConnectionStatus('connecting');
                showAlert('Testing connection to Supabase...', 'info');

                // Test connection by making a simple API call
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    method: 'GET',
                    headers: {
                        'apikey': supabaseKey,
                        'Authorization': `Bearer ${supabaseKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    updateConnectionStatus('connected');
                    showAlert('✅ Successfully connected to Supabase!', 'success');

                    // Update API status
                    document.getElementById('api-status').textContent = 'Online';
                    document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                updateConnectionStatus('error');
                showAlert(`❌ Connection failed: ${error.message}`, 'error');

                // Update API status
                document.getElementById('api-status').textContent = 'Offline';
                console.error('Connection error:', error);
            }
        }

        // Load database information
        async function loadDatabaseInfo() {
            try {
                showAlert('Loading database information...', 'info');

                const resultsDiv = document.getElementById('connection-results');
                resultsDiv.innerHTML = `
                    <div class="card">
                        <div class="card-content">
                            <span class="card-title">
                                <i class="material-icons left">storage</i>Database Information
                            </span>
                            <div class="row">
                                <div class="col s12 m6">
                                    <h6>Connection Details</h6>
                                    <p><strong>URL:</strong> ${supabaseUrl}</p>
                                    <p><strong>Project ID:</strong> ftygxwlkokwgscyflanu</p>
                                    <p><strong>Region:</strong> ap-southeast-1</p>
                                </div>
                                <div class="col s12 m6">
                                    <h6>API Information</h6>
                                    <p><strong>REST API:</strong> Available</p>
                                    <p><strong>Real-time:</strong> Enabled</p>
                                    <p><strong>Auth:</strong> Configured</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col s12">
                                    <h6>Available Endpoints</h6>
                                    <div class="collection">
                                        <a href="${supabaseUrl}/rest/v1/" target="_blank" class="collection-item">
                                            <i class="material-icons left">api</i>REST API
                                            <span class="secondary-content">
                                                <i class="material-icons green-text">check_circle</i>
                                            </span>
                                        </a>
                                        <a href="${supabaseUrl}/realtime/v1/" target="_blank" class="collection-item">
                                            <i class="material-icons left">timeline</i>Real-time API
                                            <span class="secondary-content">
                                                <i class="material-icons green-text">check_circle</i>
                                            </span>
                                        </a>
                                        <a href="${supabaseUrl}/auth/v1/" target="_blank" class="collection-item">
                                            <i class="material-icons left">security</i>Auth API
                                            <span class="secondary-content">
                                                <i class="material-icons green-text">check_circle</i>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                showAlert('✅ Database information loaded successfully!', 'success');

            } catch (error) {
                showAlert(`❌ Error loading database info: ${error.message}`, 'error');
            }
        }

        // Load database statistics
        async function loadDatabaseStats() {
            try {
                // Try to get some basic stats
                const commonTables = ['users', 'profiles', 'posts', 'products', 'orders', 'todos'];
                let tableCount = 0;
                let totalRecords = 0;

                for (const tableName of commonTables) {
                    try {
                        const { data, error } = await supabase
                            .from(tableName)
                            .select('*', { count: 'exact', head: true });

                        if (!error) {
                            tableCount++;
                            // Note: count might not be available in head request
                        }
                    } catch (e) {
                        // Table doesn't exist or no access
                    }
                }

                // Update stats
                document.getElementById('total-tables').textContent = tableCount;
                document.getElementById('total-records').textContent = totalRecords || 'N/A';

            } catch (error) {
                console.error('Stats error:', error);
                document.getElementById('total-tables').textContent = 'N/A';
                document.getElementById('total-records').textContent = 'N/A';
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                loadDatabaseStats();
                document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
            }
        }, 30000);
    </script>
</body>
</html>
