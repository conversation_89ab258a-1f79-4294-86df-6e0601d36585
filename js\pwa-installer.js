/**
 * PWA Installation Manager
 */
class PWAInstaller {
  constructor() {
    this.deferredPrompt = null;
    this.isInstalled = false;
    this.isStandalone = false;
    
    this.init();
  }

  init() {
    // Check if app is already installed
    this.checkInstallationStatus();
    
    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA: beforeinstallprompt event fired');
      e.preventDefault();
      this.deferredPrompt = e;
      this.showInstallButton();
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('PWA: App was installed');
      this.isInstalled = true;
      this.hideInstallButton();
      this.showAlert('🎉 App installed successfully! You can now use it offline.', 'success');
    });

    // Check if launched from home screen
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
      this.isStandalone = true;
      console.log('PWA: App is running in standalone mode');
    }

    // Create install button if not already present
    this.createInstallButton();
  }

  checkInstallationStatus() {
    // Check if app is running in standalone mode
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
      this.isInstalled = true;
      this.isStandalone = true;
    }

    // Check for related applications (Android)
    if ('getInstalledRelatedApps' in navigator) {
      navigator.getInstalledRelatedApps().then((relatedApps) => {
        if (relatedApps.length > 0) {
          this.isInstalled = true;
        }
      });
    }
  }

  createInstallButton() {
    // Check if button already exists
    if (document.getElementById('pwa-install-button')) {
      return;
    }

    // Create install button
    const installButton = document.createElement('button');
    installButton.id = 'pwa-install-button';
    installButton.className = 'btn waves-effect waves-light blue darken-2';
    installButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      display: none;
      border-radius: 50px;
      padding: 0 20px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
    installButton.innerHTML = '<i class="material-icons left">get_app</i>Install App';
    installButton.addEventListener('click', () => this.promptInstall());

    // Add to body
    document.body.appendChild(installButton);

    // Create install banner for better UX
    this.createInstallBanner();
  }

  createInstallBanner() {
    // Check if banner already exists
    if (document.getElementById('pwa-install-banner')) {
      return;
    }

    const banner = document.createElement('div');
    banner.id = 'pwa-install-banner';
    banner.className = 'card blue darken-2 white-text';
    banner.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1001;
      display: none;
      margin: 0;
      border-radius: 0;
      animation: slideDown 0.3s ease-out;
    `;

    banner.innerHTML = `
      <div class="card-content" style="padding: 16px;">
        <div class="row valign-wrapper" style="margin: 0;">
          <div class="col s1">
            <i class="material-icons">get_app</i>
          </div>
          <div class="col s8">
            <span class="card-title" style="font-size: 1.1rem; margin: 0;">Install Todo PWA</span>
            <p style="margin: 0; font-size: 0.9rem;">Get the full app experience with offline access</p>
          </div>
          <div class="col s3 right-align">
            <button class="btn-flat white-text" onclick="pwaInstaller.promptInstall()" style="margin-right: 8px;">Install</button>
            <button class="btn-flat white-text" onclick="pwaInstaller.dismissBanner()">
              <i class="material-icons">close</i>
            </button>
          </div>
        </div>
      </div>
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideDown {
        from { transform: translateY(-100%); }
        to { transform: translateY(0); }
      }
      @keyframes slideUp {
        from { transform: translateY(0); }
        to { transform: translateY(-100%); }
      }
      .slide-up {
        animation: slideUp 0.3s ease-in forwards;
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(banner);
  }

  showInstallButton() {
    if (this.isInstalled) return;

    const button = document.getElementById('pwa-install-button');
    if (button) {
      button.style.display = 'block';
    }

    // Show banner after a delay if user hasn't dismissed it
    const bannerDismissed = localStorage.getItem('pwa-banner-dismissed');
    if (!bannerDismissed) {
      setTimeout(() => {
        this.showInstallBanner();
      }, 3000);
    }
  }

  hideInstallButton() {
    const button = document.getElementById('pwa-install-button');
    if (button) {
      button.style.display = 'none';
    }
    this.hideInstallBanner();
  }

  showInstallBanner() {
    const banner = document.getElementById('pwa-install-banner');
    if (banner && !this.isInstalled) {
      banner.style.display = 'block';
    }
  }

  hideInstallBanner() {
    const banner = document.getElementById('pwa-install-banner');
    if (banner) {
      banner.style.display = 'none';
    }
  }

  dismissBanner() {
    this.hideInstallBanner();
    localStorage.setItem('pwa-banner-dismissed', 'true');
  }

  async promptInstall() {
    if (!this.deferredPrompt) {
      // Fallback for browsers that don't support beforeinstallprompt
      this.showManualInstallInstructions();
      return;
    }

    try {
      // Show the install prompt
      this.deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await this.deferredPrompt.userChoice;
      
      console.log(`PWA: User response to install prompt: ${outcome}`);
      
      if (outcome === 'accepted') {
        this.showAlert('🎉 Thanks for installing the app!', 'success');
      } else {
        this.showAlert('📱 You can install the app anytime from the browser menu', 'info');
      }
      
      // Clear the deferredPrompt
      this.deferredPrompt = null;
      this.hideInstallButton();
      
    } catch (error) {
      console.error('PWA: Error showing install prompt:', error);
      this.showManualInstallInstructions();
    }
  }

  showManualInstallInstructions() {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      instructions = 'Click the menu (⋮) → "Install Todo PWA" or look for the install icon in the address bar';
    } else if (userAgent.includes('firefox')) {
      instructions = 'Click the menu (☰) → "Install this site as an app"';
    } else if (userAgent.includes('safari')) {
      instructions = 'Tap the Share button → "Add to Home Screen"';
    } else if (userAgent.includes('edg')) {
      instructions = 'Click the menu (⋯) → "Apps" → "Install this site as an app"';
    } else {
      instructions = 'Look for "Install" or "Add to Home Screen" option in your browser menu';
    }

    // Create modal for instructions
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'install-instructions-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <h4><i class="material-icons left">get_app</i>Install Todo PWA</h4>
        <p>To install this app on your device:</p>
        <p><strong>${instructions}</strong></p>
        <p>Once installed, you'll be able to:</p>
        <ul class="collection">
          <li class="collection-item">📱 Use the app offline</li>
          <li class="collection-item">🚀 Launch from your home screen</li>
          <li class="collection-item">🔄 Automatic background sync</li>
          <li class="collection-item">📬 Receive notifications</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button class="modal-close waves-effect waves-green btn-flat">Got it</button>
      </div>
    `;

    document.body.appendChild(modal);
    
    // Initialize and open modal
    const modalInstance = M.Modal.init(modal);
    modalInstance.open();

    // Remove modal after closing
    modal.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal-close')) {
        setTimeout(() => {
          document.body.removeChild(modal);
        }, 300);
      }
    });
  }

  // Check if app can be installed
  canInstall() {
    return !!this.deferredPrompt && !this.isInstalled;
  }

  // Get installation status
  getStatus() {
    return {
      isInstalled: this.isInstalled,
      isStandalone: this.isStandalone,
      canInstall: this.canInstall()
    };
  }

  // Utility method to show alerts
  showAlert(message, type) {
    if (typeof window.showAlert === 'function') {
      window.showAlert(message, type);
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }
}

// Create global instance
window.pwaInstaller = new PWAInstaller();
