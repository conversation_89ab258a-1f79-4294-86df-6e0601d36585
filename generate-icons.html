<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 5px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>PWA Icon Generator</h1>
    <p>This tool generates simple PWA icons for your Todo app.</p>
    
    <div id="icons-container"></div>
    
    <button onclick="generateAllIcons()">Generate All Icons</button>
    <button onclick="downloadAllIcons()">Download All Icons</button>

    <script>
        const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
        const canvases = {};

        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.1;
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';

            // Add checkmark icon
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.08;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            const centerX = size / 2;
            const centerY = size / 2;
            const checkSize = size * 0.3;

            ctx.beginPath();
            ctx.moveTo(centerX - checkSize, centerY);
            ctx.lineTo(centerX - checkSize/3, centerY + checkSize/2);
            ctx.lineTo(centerX + checkSize, centerY - checkSize/2);
            ctx.stroke();

            return canvas;
        }

        function generateAllIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '';

            iconSizes.forEach(size => {
                const canvas = createIcon(size);
                canvases[size] = canvas;

                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadIcon(size);

                preview.appendChild(canvas);
                preview.appendChild(label);
                preview.appendChild(downloadBtn);
                container.appendChild(preview);
            });
        }

        function downloadIcon(size) {
            const canvas = canvases[size];
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAllIcons() {
            iconSizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size); // Stagger downloads
            });
        }

        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }

        // Generate icons on page load
        window.addEventListener('load', generateAllIcons);
    </script>
</body>
</html>
