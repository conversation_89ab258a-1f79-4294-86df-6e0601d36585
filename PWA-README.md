# Supabase Todo PWA

A Progressive Web App (PWA) for managing todos with offline capabilities and automatic synchronization.

## Features

### 🌐 Progressive Web App
- **Installable**: Can be installed on desktop and mobile devices
- **Offline Support**: Works without internet connection
- **Background Sync**: Automatically syncs data when connection is restored
- **Responsive Design**: Works on all screen sizes

### 📱 Offline Capabilities
- **Offline CRUD**: Create, read, update, and delete todos offline
- **Local Storage**: Uses IndexedDB for offline data persistence
- **Sync Queue**: Queues offline changes for synchronization
- **Conflict Resolution**: Handles data conflicts when syncing

### 🔄 Synchronization
- **Automatic Sync**: Syncs when connection is restored
- **Manual Sync**: Force sync with the "Sync Now" button
- **Background Sync**: Uses Service Worker for background synchronization
- **Real-time Updates**: Shows sync status and pending operations

## Installation

### Prerequisites
- Web server (XAMPP, Apache, Nginx, etc.)
- Modern web browser with PWA support
- Supabase account and project

### Setup Steps

1. **Clone/Download** the files to your web server directory
2. **Generate Icons** (optional):
   - Open `generate-icons.html` in your browser
   - Click "Generate All Icons" and "Download All Icons"
   - Place the downloaded icons in the `/icons/` folder

3. **Configure Supabase**:
   - Update the Supabase URL and API key in the HTML files
   - Ensure your Supabase project has a `todos` table

4. **Serve the Files**:
   - Make sure files are served over HTTPS (required for PWA)
   - Access the app through your web server

## File Structure

```
/
├── index.html              # Main dashboard
├── todos_crud.html         # Todo CRUD interface (main PWA functionality)
├── supabase_app.html       # Database explorer
├── manifest.json           # PWA manifest
├── sw.js                   # Service Worker
├── generate-icons.html     # Icon generator utility
├── js/
│   ├── db.js              # IndexedDB wrapper
│   ├── sync-manager.js    # Sync management
│   └── pwa-installer.js   # PWA installation handler
├── icons/                 # PWA icons (generate using generate-icons.html)
└── PWA-README.md          # This file
```

## Usage

### Installing the PWA
1. Open the app in a supported browser
2. Look for the install prompt or install button
3. Click "Install" to add the app to your device
4. The app will appear in your app drawer/start menu

### Using Offline Features
1. **Create Todos Offline**: Add new todos even without internet
2. **Edit Todos Offline**: Modify existing todos offline
3. **Delete Todos Offline**: Remove todos offline
4. **View Sync Status**: Check pending sync operations in the statistics
5. **Manual Sync**: Use the "Sync Now" button to force synchronization

### Sync Management
- **Automatic**: App automatically syncs when connection is restored
- **Manual**: Click "Sync Now" button to force sync
- **Status**: View sync queue count in the statistics section
- **Clear Data**: Use "Clear Offline Data" to reset local storage

## Technical Details

### Service Worker
- Caches static assets for offline use
- Handles background sync
- Manages network requests and fallbacks

### IndexedDB Storage
- Stores todos locally for offline access
- Maintains sync queue for pending operations
- Handles temporary IDs for offline-created todos

### Sync Strategy
- **Online**: Direct operations with Supabase + local cache update
- **Offline**: Local operations + queue for sync
- **Sync**: Process queue when connection restored

### Browser Support
- Chrome/Edge: Full PWA support
- Firefox: PWA support (limited install options)
- Safari: Basic PWA support
- Mobile browsers: Good PWA support

## Troubleshooting

### PWA Not Installing
- Ensure HTTPS is enabled
- Check browser PWA support
- Verify manifest.json is accessible
- Check for console errors

### Offline Features Not Working
- Verify Service Worker registration
- Check IndexedDB support
- Ensure JavaScript is enabled
- Check browser console for errors

### Sync Issues
- Check internet connection
- Verify Supabase credentials
- Check sync queue in statistics
- Try manual sync

### Performance Issues
- Clear offline data if storage is full
- Check for large sync queues
- Restart the app
- Clear browser cache

## Development

### Adding New Features
1. Update the appropriate HTML file
2. Modify JavaScript functions as needed
3. Update Service Worker if caching changes needed
4. Test offline functionality

### Customization
- **Colors**: Update CSS variables and manifest theme colors
- **Icons**: Generate new icons using the icon generator
- **Features**: Add new CRUD operations following existing patterns

## Security Notes

- API keys are exposed in client-side code (use Supabase RLS)
- Implement proper Row Level Security in Supabase
- Consider authentication for production use
- Validate data on both client and server side

## License

This project is open source. Modify and use as needed for your projects.
