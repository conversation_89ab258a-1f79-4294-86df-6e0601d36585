/**
 * IndexedDB wrapper for offline data storage
 */
class TodoDB {
  constructor() {
    this.dbName = 'TodoPWADB';
    this.version = 1;
    this.db = null;
  }

  // Initialize database
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('IndexedDB: Error opening database:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('IndexedDB: Database opened successfully');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log('IndexedDB: Upgrading database...');

        // Create todos store
        if (!db.objectStoreNames.contains('todos')) {
          const todosStore = db.createObjectStore('todos', { keyPath: 'id' });
          todosStore.createIndex('completed', 'completed', { unique: false });
          todosStore.createIndex('created_at', 'created_at', { unique: false });
          todosStore.createIndex('updated_at', 'updated_at', { unique: false });
          console.log('IndexedDB: Created todos store');
        }

        // Create sync queue store
        if (!db.objectStoreNames.contains('syncQueue')) {
          const syncStore = db.createObjectStore('syncQueue', { keyPath: 'id', autoIncrement: true });
          syncStore.createIndex('timestamp', 'timestamp', { unique: false });
          syncStore.createIndex('operation', 'operation', { unique: false });
          syncStore.createIndex('todoId', 'todoId', { unique: false });
          console.log('IndexedDB: Created syncQueue store');
        }

        // Create app settings store
        if (!db.objectStoreNames.contains('settings')) {
          const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
          console.log('IndexedDB: Created settings store');
        }
      };
    });
  }

  // Generic method to perform transactions
  async performTransaction(storeName, mode, operation) {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], mode);
      const store = transaction.objectStore(storeName);

      transaction.onerror = () => reject(transaction.error);
      transaction.oncomplete = () => resolve();

      const request = operation(store);
      if (request) {
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      }
    });
  }

  // Todo operations
  async getAllTodos() {
    return this.performTransaction('todos', 'readonly', (store) => store.getAll());
  }

  async getTodo(id) {
    return this.performTransaction('todos', 'readonly', (store) => store.get(id));
  }

  async addTodo(todo) {
    // Add timestamp if not present
    if (!todo.created_at) {
      todo.created_at = new Date().toISOString();
    }
    if (!todo.updated_at) {
      todo.updated_at = new Date().toISOString();
    }

    return this.performTransaction('todos', 'readwrite', (store) => store.add(todo));
  }

  async updateTodo(todo) {
    todo.updated_at = new Date().toISOString();
    return this.performTransaction('todos', 'readwrite', (store) => store.put(todo));
  }

  async deleteTodo(id) {
    return this.performTransaction('todos', 'readwrite', (store) => store.delete(id));
  }

  async clearAllTodos() {
    return this.performTransaction('todos', 'readwrite', (store) => store.clear());
  }

  // Sync queue operations
  async addToSyncQueue(operation, todoData, todoId = null) {
    const syncItem = {
      operation: operation, // 'create', 'update', 'delete'
      todoData: todoData,
      todoId: todoId,
      timestamp: Date.now(),
      retryCount: 0
    };

    return this.performTransaction('syncQueue', 'readwrite', (store) => store.add(syncItem));
  }

  async getSyncQueue() {
    return this.performTransaction('syncQueue', 'readonly', (store) => store.getAll());
  }

  async removeFromSyncQueue(id) {
    return this.performTransaction('syncQueue', 'readwrite', (store) => store.delete(id));
  }

  async clearSyncQueue() {
    return this.performTransaction('syncQueue', 'readwrite', (store) => store.clear());
  }

  async updateSyncQueueItem(id, updates) {
    return this.performTransaction('syncQueue', 'readwrite', async (store) => {
      const item = await new Promise((resolve, reject) => {
        const request = store.get(id);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
      });

      if (item) {
        Object.assign(item, updates);
        return store.put(item);
      }
    });
  }

  // Settings operations
  async getSetting(key) {
    return this.performTransaction('settings', 'readonly', (store) => store.get(key));
  }

  async setSetting(key, value) {
    const setting = { key, value, updated_at: new Date().toISOString() };
    return this.performTransaction('settings', 'readwrite', (store) => store.put(setting));
  }

  // Utility methods
  async getLastSyncTime() {
    const setting = await this.getSetting('lastSyncTime');
    return setting ? setting.value : null;
  }

  async setLastSyncTime(timestamp) {
    return this.setSetting('lastSyncTime', timestamp);
  }

  async isOnline() {
    return navigator.onLine;
  }

  // Generate temporary ID for offline todos
  generateTempId() {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Check if ID is temporary
  isTempId(id) {
    return typeof id === 'string' && id.startsWith('temp_');
  }

  // Database statistics
  async getStats() {
    const todos = await this.getAllTodos();
    const syncQueue = await this.getSyncQueue();
    
    return {
      totalTodos: todos.length,
      completedTodos: todos.filter(todo => todo.completed).length,
      pendingSync: syncQueue.length,
      lastSync: await this.getLastSyncTime()
    };
  }

  // Close database connection
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('IndexedDB: Database connection closed');
    }
  }
}

// Export for use in other files
window.TodoDB = TodoDB;
