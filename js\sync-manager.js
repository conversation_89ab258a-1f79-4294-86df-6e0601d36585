/**
 * Sync Manager for handling online/offline state and data synchronization
 */
class SyncManager {
  constructor(supabaseClient, todoDB) {
    this.supabase = supabaseClient;
    this.db = todoDB;
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second

    this.init();
  }

  init() {
    // Listen for online/offline events
    window.addEventListener('online', () => this.handleOnline());
    window.addEventListener('offline', () => this.handleOffline());

    // Listen for service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'SYNC_TODOS') {
          this.syncPendingOperations(event.data.pendingOps);
        }
      });
    }

    // Update initial status
    this.updateConnectionStatus();

    // Periodic sync check (every 30 seconds when online)
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncPendingOperations();
      }
    }, 30000);
  }

  handleOnline() {
    console.log('SyncManager: Device is online');
    this.isOnline = true;
    this.updateConnectionStatus();
    this.showAlert('🌐 Back online! Syncing data...', 'info');
    
    // Trigger sync after a short delay
    setTimeout(() => {
      this.syncPendingOperations();
    }, 1000);
  }

  handleOffline() {
    console.log('SyncManager: Device is offline');
    this.isOnline = false;
    this.updateConnectionStatus();
    this.showAlert('📱 You are offline. Changes will be saved locally and synced when connection is restored.', 'warning');
  }

  updateConnectionStatus() {
    const statusIndicator = document.getElementById('nav-status-indicator');
    const statusText = document.getElementById('nav-status-text');
    
    if (!statusIndicator || !statusText) return;

    if (this.isOnline) {
      statusIndicator.className = 'status-indicator status-connected';
      statusText.textContent = this.syncInProgress ? 'Syncing...' : 'Online';
    } else {
      statusIndicator.className = 'status-indicator status-offline';
      statusText.textContent = 'Offline';
    }
  }

  async syncPendingOperations(pendingOps = null) {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    try {
      this.syncInProgress = true;
      this.updateConnectionStatus();

      // Get pending operations from IndexedDB if not provided
      if (!pendingOps) {
        pendingOps = await this.db.getSyncQueue();
      }

      if (pendingOps.length === 0) {
        console.log('SyncManager: No pending operations to sync');
        return;
      }

      console.log(`SyncManager: Syncing ${pendingOps.length} pending operations`);

      let successCount = 0;
      let failureCount = 0;

      for (const operation of pendingOps) {
        try {
          await this.syncSingleOperation(operation);
          await this.db.removeFromSyncQueue(operation.id);
          successCount++;
        } catch (error) {
          console.error('SyncManager: Failed to sync operation:', operation, error);
          
          // Increment retry count
          operation.retryCount = (operation.retryCount || 0) + 1;
          
          if (operation.retryCount >= this.retryAttempts) {
            console.error('SyncManager: Max retries reached for operation:', operation);
            await this.db.removeFromSyncQueue(operation.id);
            failureCount++;
          } else {
            await this.db.updateSyncQueueItem(operation.id, { 
              retryCount: operation.retryCount 
            });
          }
        }
      }

      // Update last sync time
      await this.db.setLastSyncTime(Date.now());

      // Show sync results
      if (successCount > 0) {
        this.showAlert(`✅ Synced ${successCount} changes successfully`, 'success');
      }
      
      if (failureCount > 0) {
        this.showAlert(`⚠️ Failed to sync ${failureCount} changes`, 'error');
      }

      // Trigger a fresh load of todos to get latest data
      if (typeof window.loadTodos === 'function') {
        await window.loadTodos();
      }

    } catch (error) {
      console.error('SyncManager: Error during sync:', error);
      this.showAlert('❌ Sync failed. Will retry automatically.', 'error');
    } finally {
      this.syncInProgress = false;
      this.updateConnectionStatus();
    }
  }

  async syncSingleOperation(operation) {
    const { operation: opType, todoData, todoId } = operation;

    switch (opType) {
      case 'create':
        return await this.syncCreate(todoData);
      
      case 'update':
        return await this.syncUpdate(todoId, todoData);
      
      case 'delete':
        return await this.syncDelete(todoId);
      
      default:
        throw new Error(`Unknown operation type: ${opType}`);
    }
  }

  async syncCreate(todoData) {
    // Remove temporary ID and local timestamps
    const { id, ...dataToSync } = todoData;
    
    const { data, error } = await this.supabase
      .from('todos')
      .insert([dataToSync])
      .select();

    if (error) throw error;

    // Update local todo with real ID
    if (data && data[0]) {
      await this.db.deleteTodo(id); // Remove temp todo
      await this.db.addTodo(data[0]); // Add real todo
    }

    return data;
  }

  async syncUpdate(todoId, todoData) {
    // If it's a temp ID, we need to find the real ID first
    if (this.db.isTempId(todoId)) {
      // This shouldn't happen if sync order is correct, but handle it
      throw new Error('Cannot update todo with temporary ID');
    }

    const { data, error } = await this.supabase
      .from('todos')
      .update(todoData)
      .eq('id', todoId)
      .select();

    if (error) throw error;

    // Update local todo
    if (data && data[0]) {
      await this.db.updateTodo(data[0]);
    }

    return data;
  }

  async syncDelete(todoId) {
    // If it's a temp ID, just remove from local storage
    if (this.db.isTempId(todoId)) {
      await this.db.deleteTodo(todoId);
      return;
    }

    const { error } = await this.supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) throw error;

    // Remove from local storage
    await this.db.deleteTodo(todoId);
  }

  // Force sync (called manually)
  async forcSync() {
    if (!this.isOnline) {
      this.showAlert('❌ Cannot sync while offline', 'error');
      return;
    }

    this.showAlert('🔄 Starting manual sync...', 'info');
    await this.syncPendingOperations();
  }

  // Get sync status
  async getSyncStatus() {
    const stats = await this.db.getStats();
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      pendingOperations: stats.pendingSync,
      lastSync: stats.lastSync
    };
  }

  // Utility method to show alerts (should be implemented in main app)
  showAlert(message, type) {
    if (typeof window.showAlert === 'function') {
      window.showAlert(message, type);
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }

  // Register for background sync (if supported)
  async registerBackgroundSync() {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('sync-todos');
        console.log('SyncManager: Background sync registered');
      } catch (error) {
        console.error('SyncManager: Failed to register background sync:', error);
      }
    }
  }
}

// Export for use in other files
window.SyncManager = SyncManager;
